/**
 * MetaMask钱包适配器
 */

import { WalletAdapter } from './WalletAdapter';
import { BlockchainNetwork, Transaction, WalletType, Web3Provider } from '../types/BlockchainTypes';

declare global {
  interface Window {
    ethereum?: Web3Provider;
  }
}

export class MetaMaskAdapter extends WalletAdapter {
  private provider: Web3Provider | null = null;

  constructor() {
    super(WalletType.METAMASK);
  }

  /**
   * 检查MetaMask是否可用
   */
  async isAvailable(): Promise<boolean> {
    return typeof window !== 'undefined' && 
           typeof window.ethereum !== 'undefined' && 
           window.ethereum.isMetaMask === true;
  }

  /**
   * 检查MetaMask是否已连接
   */
  async isConnected(): Promise<boolean> {
    try {
      if (!await this.isAvailable()) {
        return false;
      }

      this.provider = window.ethereum!;
      const accounts = await this.provider.request({
        method: 'eth_accounts'
      });

      return Array.isArray(accounts) && accounts.length > 0;
    } catch (error) {
      console.error('检查MetaMask连接状态失败:', error);
      return false;
    }
  }

  /**
   * 连接MetaMask
   */
  async connect(): Promise<string> {
    try {
      if (!await this.isAvailable()) {
        throw new Error('MetaMask未安装或不可用');
      }

      this.provider = window.ethereum!;

      // 请求连接账户
      const accounts = await this.provider.request({
        method: 'eth_requestAccounts'
      });

      if (!accounts || accounts.length === 0) {
        throw new Error('未获取到账户信息');
      }

      const address = this.formatAddress(accounts[0]);
      
      // 初始化事件监听器
      if (!this.isInitialized) {
        await this.initialize();
      }

      console.log('MetaMask连接成功:', address);
      return address;

    } catch (error) {
      throw this.handleWalletError(error);
    }
  }

  /**
   * 断开MetaMask连接
   */
  async disconnect(): Promise<void> {
    try {
      // MetaMask没有直接的断开连接方法
      // 只能清理本地状态
      this.provider = null;
      console.log('MetaMask连接已清理');
    } catch (error) {
      console.error('断开MetaMask连接失败:', error);
    }
  }

  /**
   * 获取当前账户地址
   */
  async getAddress(): Promise<string> {
    try {
      if (!this.provider) {
        throw new Error('MetaMask未连接');
      }

      const accounts = await this.provider.request({
        method: 'eth_accounts'
      });

      if (!accounts || accounts.length === 0) {
        throw new Error('未获取到账户信息');
      }

      return this.formatAddress(accounts[0]);
    } catch (error) {
      throw this.handleWalletError(error);
    }
  }

  /**
   * 获取账户余额
   */
  async getBalance(address?: string): Promise<string> {
    try {
      if (!this.provider) {
        throw new Error('MetaMask未连接');
      }

      const targetAddress = address || await this.getAddress();
      
      const balance = await this.provider.request({
        method: 'eth_getBalance',
        params: [targetAddress, 'latest']
      });

      return this.weiToEther(balance);
    } catch (error) {
      console.error('获取余额失败:', error);
      return '0';
    }
  }

  /**
   * 获取当前链ID
   */
  async getChainId(): Promise<number> {
    try {
      if (!this.provider) {
        throw new Error('MetaMask未连接');
      }

      const chainId = await this.provider.request({
        method: 'eth_chainId'
      });

      return parseInt(chainId, 16);
    } catch (error) {
      throw this.handleWalletError(error);
    }
  }

  /**
   * 切换网络
   */
  async switchNetwork(network: BlockchainNetwork): Promise<void> {
    try {
      if (!this.provider) {
        throw new Error('MetaMask未连接');
      }

      const chainIdHex = '0x' + network.chainId.toString(16);

      try {
        // 尝试切换到指定网络
        await this.provider.request({
          method: 'wallet_switchEthereumChain',
          params: [{ chainId: chainIdHex }]
        });
      } catch (switchError: any) {
        // 如果网络不存在，尝试添加网络
        if (switchError.code === 4902) {
          await this.provider.request({
            method: 'wallet_addEthereumChain',
            params: [{
              chainId: chainIdHex,
              chainName: network.name,
              rpcUrls: [network.rpcUrl],
              blockExplorerUrls: [network.blockExplorer],
              nativeCurrency: network.nativeCurrency
            }]
          });
        } else {
          throw switchError;
        }
      }
    } catch (error) {
      throw this.handleWalletError(error);
    }
  }

  /**
   * 发送交易
   */
  async sendTransaction(transaction: Partial<Transaction>): Promise<string> {
    try {
      if (!this.provider) {
        throw new Error('MetaMask未连接');
      }

      const fromAddress = await this.getAddress();
      const params = this.formatTransactionParams({
        ...transaction,
        from: fromAddress
      });

      const txHash = await this.provider.request({
        method: 'eth_sendTransaction',
        params: [params]
      });

      return txHash;
    } catch (error) {
      throw this.handleWalletError(error);
    }
  }

  /**
   * 签名消息
   */
  async signMessage(message: string): Promise<string> {
    try {
      if (!this.provider) {
        throw new Error('MetaMask未连接');
      }

      const address = await this.getAddress();
      
      const signature = await this.provider.request({
        method: 'personal_sign',
        params: [message, address]
      });

      return signature;
    } catch (error) {
      throw this.handleWalletError(error);
    }
  }

  /**
   * 签名类型化数据
   */
  async signTypedData(domain: any, types: any, value: any): Promise<string> {
    try {
      if (!this.provider) {
        throw new Error('MetaMask未连接');
      }

      const address = await this.getAddress();
      
      const signature = await this.provider.request({
        method: 'eth_signTypedData_v4',
        params: [address, JSON.stringify({ domain, types, value })]
      });

      return signature;
    } catch (error) {
      throw this.handleWalletError(error);
    }
  }

  /**
   * 添加代币到MetaMask
   */
  async addToken(tokenAddress: string, tokenSymbol: string, tokenDecimals: number, tokenImage?: string): Promise<boolean> {
    try {
      if (!this.provider) {
        throw new Error('MetaMask未连接');
      }

      const result = await this.provider.request({
        method: 'wallet_watchAsset',
        params: {
          type: 'ERC20',
          options: {
            address: tokenAddress,
            symbol: tokenSymbol,
            decimals: tokenDecimals,
            image: tokenImage
          }
        }
      });

      return result;
    } catch (error) {
      console.error('添加代币失败:', error);
      return false;
    }
  }

  /**
   * 设置账户变化监听
   */
  protected setupAccountListener(): void {
    if (!this.provider) return;

    this.provider.on('accountsChanged', (accounts: string[]) => {
      this.emit('accountsChanged', accounts.map(addr => this.formatAddress(addr)));
    });
  }

  /**
   * 设置网络变化监听
   */
  protected setupNetworkListener(): void {
    if (!this.provider) return;

    this.provider.on('chainChanged', (chainId: string) => {
      const numericChainId = parseInt(chainId, 16);
      this.emit('chainChanged', numericChainId);
    });
  }

  /**
   * 设置连接状态监听
   */
  protected setupConnectionListener(): void {
    if (!this.provider) return;

    this.provider.on('connect', (connectInfo: { chainId: string }) => {
      const chainId = parseInt(connectInfo.chainId, 16);
      this.emit('connect', chainId);
    });

    this.provider.on('disconnect', (error: any) => {
      this.emit('disconnect', error);
    });
  }
}

/**
 * 场景RAG应用工厂
 * 简化RAG应用的创建和配置
 */
import type { Scene } from './Scene';
import type { Entity } from '../core/Entity';
import { SceneRAGComponent, RAGApplicationConfig } from './components/SceneRAGComponent';
import type { SceneAvatarConfig } from './SceneAvatarManager';
import type { EnhancedAvatarConfig } from '../avatar/components/EnhancedAvatarComponent';
import type { VoiceConfig } from '../voice/VoiceInteractionComponent';

/**
 * RAG应用模板
 */
export interface RAGApplicationTemplate {
  /** 模板ID */
  id: string;
  /** 模板名称 */
  name: string;
  /** 模板描述 */
  description: string;
  /** 应用类型 */
  type: 'medical' | 'education' | 'customer_service' | 'entertainment' | 'custom';
  /** 默认配置 */
  defaultConfig: Partial<RAGApplicationConfig>;
  /** 数字人模板 */
  avatarTemplates: AvatarTemplate[];
}

/**
 * 数字人模板
 */
export interface AvatarTemplate {
  /** 模板ID */
  id: string;
  /** 模板名称 */
  name: string;
  /** 角色类型 */
  role: 'doctor' | 'teacher' | 'assistant' | 'guide' | 'custom';
  /** 默认位置 */
  defaultPosition: { x: number; y: number; z: number };
  /** 数字人配置 */
  avatarConfig: Partial<EnhancedAvatarConfig>;
  /** 语音配置 */
  voiceConfig: Partial<VoiceConfig>;
}

/**
 * 创建选项
 */
export interface CreateRAGApplicationOptions {
  /** 应用名称 */
  name: string;
  /** 应用描述 */
  description?: string;
  /** 知识库ID */
  knowledgeBaseId: string;
  /** 使用的模板ID */
  templateId?: string;
  /** 数字人配置 */
  avatars?: Partial<SceneAvatarConfig>[];
  /** 自定义配置 */
  customConfig?: Partial<RAGApplicationConfig>;
  /** 场景位置 */
  scenePosition?: { x: number; y: number; z: number };
}

/**
 * 场景RAG应用工厂
 */
export class SceneRAGApplicationFactory {
  /** 预定义模板 */
  private static templates: Map<string, RAGApplicationTemplate> = new Map();

  /**
   * 初始化预定义模板
   */
  static {
    // 医疗咨询模板
    SceneRAGApplicationFactory.templates.set('medical_consultation', {
      id: 'medical_consultation',
      name: '医疗咨询助手',
      description: '专业的医疗咨询数字人助手',
      type: 'medical',
      defaultConfig: {
        maxConcurrentSessions: 5,
        sessionTimeout: 1800, // 30分钟
        welcomeMessage: '您好，我是您的医疗咨询助手，请问有什么可以帮助您的吗？',
        farewellMessage: '感谢您的咨询，祝您身体健康！',
        autoStart: true,
      },
      avatarTemplates: [
        {
          id: 'doctor_female',
          name: '女医生',
          role: 'doctor',
          defaultPosition: { x: 0, y: 0, z: 0 },
          avatarConfig: {
            appearance: {
              model: '/models/doctor_female.glb',
              texture: '/textures/doctor_uniform.jpg',
            },
            dialogue: {
              personality: {
                type: 'professional',
                traits: ['caring', 'knowledgeable', 'patient'],
                responseStyle: 'detailed_professional',
              },
            },
          },
          voiceConfig: {
            synthesis: {
              provider: 'azure',
              voice: 'zh-CN-XiaoxiaoNeural',
              language: 'zh-CN',
              style: 'friendly',
              emotion: 'calm',
            },
          },
        },
      ],
    });

    // 教育培训模板
    SceneRAGApplicationFactory.templates.set('education_training', {
      id: 'education_training',
      name: '教育培训助手',
      description: '智能教育培训数字人',
      type: 'education',
      defaultConfig: {
        maxConcurrentSessions: 10,
        sessionTimeout: 3600, // 60分钟
        welcomeMessage: '欢迎来到学习中心，我是您的学习助手，让我们开始学习吧！',
        farewellMessage: '学习愉快，期待下次见面！',
        autoStart: true,
      },
      avatarTemplates: [
        {
          id: 'teacher_male',
          name: '男老师',
          role: 'teacher',
          defaultPosition: { x: 0, y: 0, z: 0 },
          avatarConfig: {
            appearance: {
              model: '/models/teacher_male.glb',
              texture: '/textures/teacher_casual.jpg',
            },
            dialogue: {
              personality: {
                type: 'friendly',
                traits: ['encouraging', 'patient', 'knowledgeable'],
                responseStyle: 'educational_supportive',
              },
            },
          },
          voiceConfig: {
            synthesis: {
              provider: 'azure',
              voice: 'zh-CN-YunxiNeural',
              language: 'zh-CN',
              style: 'friendly',
              emotion: 'cheerful',
            },
          },
        },
      ],
    });

    // 客服助手模板
    SceneRAGApplicationFactory.templates.set('customer_service', {
      id: 'customer_service',
      name: '客服助手',
      description: '智能客服数字人',
      type: 'customer_service',
      defaultConfig: {
        maxConcurrentSessions: 20,
        sessionTimeout: 900, // 15分钟
        welcomeMessage: '您好，我是智能客服助手，很高兴为您服务！',
        farewellMessage: '感谢您的咨询，祝您生活愉快！',
        autoStart: true,
      },
      avatarTemplates: [
        {
          id: 'service_female',
          name: '客服小姐',
          role: 'assistant',
          defaultPosition: { x: 0, y: 0, z: 0 },
          avatarConfig: {
            appearance: {
              model: '/models/service_female.glb',
              texture: '/textures/business_suit.jpg',
            },
            dialogue: {
              personality: {
                type: 'professional',
                traits: ['helpful', 'efficient', 'polite'],
                responseStyle: 'service_oriented',
              },
            },
          },
          voiceConfig: {
            synthesis: {
              provider: 'azure',
              voice: 'zh-CN-XiaoxiaoNeural',
              language: 'zh-CN',
              style: 'customerservice',
              emotion: 'friendly',
            },
          },
        },
      ],
    });
  }

  /**
   * 创建RAG应用
   */
  public static async createRAGApplication(
    scene: Scene,
    options: CreateRAGApplicationOptions
  ): Promise<Entity> {
    // 获取模板
    const template = options.templateId 
      ? SceneRAGApplicationFactory.templates.get(options.templateId)
      : null;

    // 生成应用ID
    const appId = `rag_app_${Date.now()}`;

    // 构建数字人配置
    const avatarConfigs: SceneAvatarConfig[] = [];
    
    if (options.avatars && options.avatars.length > 0) {
      // 使用用户提供的数字人配置
      for (let i = 0; i < options.avatars.length; i++) {
        const avatarConfig = options.avatars[i];
        const avatarId = `${appId}_avatar_${i}`;
        
        avatarConfigs.push({
          avatarId,
          name: avatarConfig.name || `数字人${i + 1}`,
          knowledgeBaseId: options.knowledgeBaseId,
          ragApplicationId: appId,
          position: avatarConfig.position || options.scenePosition || { x: i * 2, y: 0, z: 0 },
          rotation: avatarConfig.rotation || { x: 0, y: 0, z: 0 },
          scale: avatarConfig.scale || { x: 1, y: 1, z: 1 },
          autoStart: avatarConfig.autoStart !== undefined ? avatarConfig.autoStart : true,
          interactionRadius: avatarConfig.interactionRadius || 2.0,
          voiceConfig: avatarConfig.voiceConfig,
          avatarConfig: avatarConfig.avatarConfig,
        });
      }
    } else if (template) {
      // 使用模板创建数字人
      for (let i = 0; i < template.avatarTemplates.length; i++) {
        const avatarTemplate = template.avatarTemplates[i];
        const avatarId = `${appId}_${avatarTemplate.id}`;
        
        avatarConfigs.push({
          avatarId,
          name: avatarTemplate.name,
          knowledgeBaseId: options.knowledgeBaseId,
          ragApplicationId: appId,
          position: options.scenePosition || avatarTemplate.defaultPosition,
          rotation: { x: 0, y: 0, z: 0 },
          scale: { x: 1, y: 1, z: 1 },
          autoStart: true,
          interactionRadius: 2.0,
          voiceConfig: {
            ...avatarTemplate.voiceConfig,
            serverUrl: 'ws://localhost:4010/voice',
          } as VoiceConfig,
          avatarConfig: avatarTemplate.avatarConfig as EnhancedAvatarConfig,
        });
      }
    } else {
      throw new Error('必须提供数字人配置或选择模板');
    }

    // 构建RAG应用配置
    const ragConfig: RAGApplicationConfig = {
      id: appId,
      name: options.name,
      description: options.description || '',
      knowledgeBaseId: options.knowledgeBaseId,
      avatars: avatarConfigs,
      autoStart: true,
      maxConcurrentSessions: 5,
      sessionTimeout: 1800,
      ...template?.defaultConfig,
      ...options.customConfig,
    };

    // 创建实体
    const entity = scene.getWorld().createEntity(`RAGApp_${options.name}`);
    
    // 设置实体位置
    if (options.scenePosition) {
      const transform = entity.getTransform();
      transform.setPosition(
        options.scenePosition.x,
        options.scenePosition.y,
        options.scenePosition.z
      );
    }

    // 添加RAG组件
    const ragComponent = new SceneRAGComponent(entity, ragConfig);
    entity.addComponent(ragComponent);

    // 添加到场景
    scene.addEntity(entity);

    // 初始化组件
    await ragComponent.initialize();

    console.log(`RAG应用 "${options.name}" 创建成功`);
    return entity;
  }

  /**
   * 获取可用模板
   */
  public static getAvailableTemplates(): RAGApplicationTemplate[] {
    return Array.from(SceneRAGApplicationFactory.templates.values());
  }

  /**
   * 获取模板
   */
  public static getTemplate(templateId: string): RAGApplicationTemplate | undefined {
    return SceneRAGApplicationFactory.templates.get(templateId);
  }

  /**
   * 注册自定义模板
   */
  public static registerTemplate(template: RAGApplicationTemplate): void {
    SceneRAGApplicationFactory.templates.set(template.id, template);
  }

  /**
   * 创建医疗咨询应用
   */
  public static async createMedicalConsultationApp(
    scene: Scene,
    name: string,
    knowledgeBaseId: string,
    position?: { x: number; y: number; z: number }
  ): Promise<Entity> {
    return SceneRAGApplicationFactory.createRAGApplication(scene, {
      name,
      knowledgeBaseId,
      templateId: 'medical_consultation',
      scenePosition: position,
    });
  }

  /**
   * 创建教育培训应用
   */
  public static async createEducationTrainingApp(
    scene: Scene,
    name: string,
    knowledgeBaseId: string,
    position?: { x: number; y: number; z: number }
  ): Promise<Entity> {
    return SceneRAGApplicationFactory.createRAGApplication(scene, {
      name,
      knowledgeBaseId,
      templateId: 'education_training',
      scenePosition: position,
    });
  }

  /**
   * 创建客服助手应用
   */
  public static async createCustomerServiceApp(
    scene: Scene,
    name: string,
    knowledgeBaseId: string,
    position?: { x: number; y: number; z: number }
  ): Promise<Entity> {
    return SceneRAGApplicationFactory.createRAGApplication(scene, {
      name,
      knowledgeBaseId,
      templateId: 'customer_service',
      scenePosition: position,
    });
  }

  /**
   * 从配置文件创建应用
   */
  public static async createFromConfig(
    scene: Scene,
    configPath: string
  ): Promise<Entity> {
    try {
      // 这里可以实现从JSON配置文件加载应用配置
      const response = await fetch(configPath);
      const config = await response.json();
      
      return SceneRAGApplicationFactory.createRAGApplication(scene, config);
    } catch (error) {
      throw new Error(`从配置文件创建应用失败: ${error.message}`);
    }
  }

  /**
   * 导出应用配置
   */
  public static exportApplicationConfig(entity: Entity): RAGApplicationConfig | null {
    const ragComponent = entity.getComponent<SceneRAGComponent>('SceneRAGComponent');
    return ragComponent ? ragComponent.getConfig() : null;
  }

  /**
   * 克隆应用
   */
  public static async cloneApplication(
    scene: Scene,
    sourceEntity: Entity,
    newName: string,
    position?: { x: number; y: number; z: number }
  ): Promise<Entity> {
    const config = SceneRAGApplicationFactory.exportApplicationConfig(sourceEntity);
    if (!config) {
      throw new Error('源实体不包含RAG组件');
    }

    // 修改配置
    const newConfig = {
      ...config,
      id: `rag_app_${Date.now()}`,
      name: newName,
    };

    // 更新数字人配置
    if (position) {
      newConfig.avatars = newConfig.avatars.map((avatar, index) => ({
        ...avatar,
        avatarId: `${newConfig.id}_avatar_${index}`,
        position: { x: position.x + index * 2, y: position.y, z: position.z },
        ragApplicationId: newConfig.id,
      }));
    }

    // 创建新实体
    const entity = scene.getWorld().createEntity(`RAGApp_${newName}`);
    
    if (position) {
      const transform = entity.getTransform();
      transform.setPosition(position.x, position.y, position.z);
    }

    const ragComponent = new SceneRAGComponent(entity, newConfig);
    entity.addComponent(ragComponent);

    scene.addEntity(entity);
    await ragComponent.initialize();

    return entity;
  }
}

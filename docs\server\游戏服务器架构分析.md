# DL引擎游戏服务器架构分析

## 概述

DL引擎的游戏服务器是一个基于Kubernetes和Agones的云原生实时游戏服务器系统，专为3D场景的多人实时交互设计。该系统集成了WebRTC通信、自动扩缩容、智能负载均衡等先进技术，为数字化学习应用提供高性能、高可用、可扩展的多人协作环境。

## 核心架构设计

### 1. 云原生架构

#### 1.1 Kubernetes集成
```yaml
# Fleet配置 - 游戏服务器集群管理
apiVersion: "agones.dev/v1"
kind: Fleet
metadata:
  name: ir-game-server-fleet
spec:
  replicas: 3
  template:
    spec:
      ports:
      - name: default
        portPolicy: Dynamic
        containerPort: 3030
        protocol: TCP
      - name: webrtc-udp
        portPolicy: Dynamic
        containerPort: 10000
        protocol: UDP
```

**核心特性**：
- **容器化部署**：基于Docker容器的微服务架构
- **服务发现**：自动服务注册与发现机制
- **配置管理**：ConfigMap和Secret统一配置管理
- **网络隔离**：Namespace级别的网络隔离

#### 1.2 Agones游戏服务器管理
```typescript
class AgonesService {
  private agonesSDK: AgonesSDK;
  private gameServerInfo: any;
  
  async ready(): Promise<boolean> {
    await this.agonesSDK.ready();
    this.gameServerInfo = await this.getGameServer();
    this.eventEmitter.emit('agones.ready', this.gameServerInfo);
    return true;
  }
  
  async allocate(): Promise<boolean> {
    await this.agonesSDK.allocate();
    this.eventEmitter.emit('agones.allocated', this.gameServerInfo);
    return true;
  }
}
```

**Agones功能**：
- **生命周期管理**：自动管理游戏服务器的创建、分配、销毁
- **健康检查**：定期检查服务器健康状态
- **资源调度**：智能分配计算资源
- **状态同步**：实时同步服务器状态信息

### 2. 自动扩缩容机制

#### 2.1 Fleet自动扩缩容
```yaml
# FleetAutoscaler配置
apiVersion: "autoscaling.agones.dev/v1"
kind: FleetAutoscaler
spec:
  fleetName: ir-game-server-fleet
  policy:
    type: Buffer
    buffer:
      bufferSize: 2      # 缓冲实例数
      minReplicas: 2     # 最小实例数
      maxReplicas: 10    # 最大实例数
```

**扩缩容策略**：
- **缓冲策略**：保持一定数量的空闲实例
- **负载感知**：基于CPU、内存、用户数量的智能扩缩容
- **预测性扩容**：基于历史数据预测负载变化
- **快速响应**：秒级扩容响应时间

#### 2.2 负载均衡算法
```typescript
class LoadBalancerService {
  // 多因素负载均衡算法
  private calculateLoadScore(instance: Instance): number {
    const metrics = this.instanceMetrics.get(instance.id);
    if (!metrics) return instance.currentUsers / instance.maxUsers;
    
    // 综合负载分数计算
    return metrics.cpuUsage * 0.4 + 
           metrics.memoryUsage * 0.3 + 
           (instance.currentUsers / instance.maxUsers) * 0.3;
  }
  
  // 智能实例选择
  findOptimalInstance(instances: Instance[]): Instance {
    return instances.reduce((optimal, current) => {
      const optimalScore = this.calculateLoadScore(optimal);
      const currentScore = this.calculateLoadScore(current);
      return currentScore < optimalScore ? current : optimal;
    });
  }
}
```

**负载均衡特性**：
- **多算法支持**：随机、轮询、加权轮询、最少响应时间
- **区域感知**：基于地理位置的智能路由
- **健康检查**：自动剔除不健康的实例
- **一致性哈希**：支持有状态服务的负载均衡

## 实例管理系统

### 1. 实例生命周期管理

#### 1.1 实例创建与分配
```typescript
class InstanceService {
  async createInstance(options: {
    sceneId?: string;
    locationId?: string;
    channelId?: string;
    isMediaInstance?: boolean;
  }): Promise<Instance> {
    const gameServerInfo = await this.agonesService.getGameServer();
    
    const instance: Instance = {
      id: uuidv4(),
      sceneId: options.sceneId,
      ipAddress: gameServerInfo.status.address,
      port: gameServerInfo.status.ports[0]?.port,
      status: 'creating',
      currentUsers: 0,
      maxUsers: this.maxUsersPerInstance,
      createdAt: new Date(),
    };
    
    this.instances.set(instance.id, instance);
    this.eventEmitter.emit('instance.created', instance);
    
    return instance;
  }
}
```

**实例管理功能**：
- **动态创建**：根据需求动态创建游戏实例
- **用户分配**：智能分配用户到合适的实例
- **状态监控**：实时监控实例运行状态
- **资源回收**：自动回收空闲实例资源

#### 1.2 用户迁移机制
```typescript
class InstanceMigrationService {
  async startMigration(sourceInstanceId: string, targetInstanceId: string): Promise<string> {
    const migrationId = `migration-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    const users = await this.instanceService.getInstanceUsers(sourceInstanceId);
    
    this.migrationInProgress.set(migrationId, {
      sourceInstanceId,
      targetInstanceId,
      startTime: new Date(),
      status: 'preparing',
      users,
    });
    
    // 开始迁移过程
    this.processMigration(migrationId);
    return migrationId;
  }
  
  private async processMigration(migrationId: string): Promise<void> {
    const migration = this.migrationInProgress.get(migrationId);
    
    // 1. 准备阶段：通知用户即将迁移
    migration.status = 'notifying';
    await this.notifyUsers(migration);
    
    // 2. 迁移阶段：转移用户连接和状态
    migration.status = 'migrating';
    await this.transferUsers(migration);
    
    // 3. 完成阶段：清理源实例
    migration.status = 'completed';
    await this.cleanupSource(migration);
  }
}
```

**迁移特性**：
- **无缝迁移**：用户无感知的实例间迁移
- **状态保持**：完整保持用户状态和会话信息
- **故障恢复**：迁移失败时的自动回滚机制
- **负载均衡**：基于负载的智能迁移决策

## WebRTC实时通信

### 1. MediaSoup集成

#### 1.1 媒体服务器配置
```typescript
class WebRTCService {
  private workers: Worker[] = [];
  private routers: Router[] = [];
  
  async initialize(): Promise<void> {
    // 创建MediaSoup工作进程
    for (let i = 0; i < this.numWorkers; i++) {
      const worker = await mediasoup.createWorker({
        logLevel: 'warn',
        rtcMinPort: 10000,
        rtcMaxPort: 59999,
      });
      
      this.workers.push(worker);
      
      // 创建路由器
      const router = await worker.createRouter({
        mediaCodecs: [
          {
            kind: 'audio',
            mimeType: 'audio/opus',
            clockRate: 48000,
            channels: 2
          },
          {
            kind: 'video',
            mimeType: 'video/VP8',
            clockRate: 90000
          }
        ]
      });
      
      this.routers.push(router);
    }
  }
}
```

**MediaSoup特性**：
- **多工作进程**：支持多核CPU的并行处理
- **编解码器支持**：支持Opus音频和VP8/H264视频
- **自适应码率**：根据网络状况自动调整质量
- **低延迟传输**：优化的RTP传输协议

#### 1.2 WebRTC连接管理
```typescript
class WebRTCConnectionManager {
  private connections: Map<string, WebRTCConnection> = new Map();
  
  async createConnection(userId: string): Promise<WebRTCConnection> {
    const connection = new WebRTCConnection(userId, this.config.iceServers, {
      enableDataChannel: true,
      enableAudio: this.config.enableAudio,
      enableVideo: this.config.enableVideo,
      useCompression: true,
    });
    
    // 设置连接事件监听
    connection.on('connected', () => {
      this.handleConnectionEstablished(userId);
    });
    
    connection.on('dataReceived', (data) => {
      this.handleDataReceived(userId, data);
    });
    
    this.connections.set(userId, connection);
    return connection;
  }
}
```

**连接管理功能**：
- **P2P连接**：支持点对点直连通信
- **数据通道**：高效的二进制数据传输
- **连接监控**：实时监控连接质量和状态
- **自动重连**：网络中断时的自动重连机制

### 2. 实时数据同步

#### 2.1 增量同步机制
```typescript
class DataSyncService {
  private lastSyncTime: Map<string, number> = new Map();
  
  sendIncrementalUpdate(userId: string, data: any): void {
    const lastSync = this.lastSyncTime.get(userId) || 0;
    const now = Date.now();
    
    // 只发送变化的数据
    const deltaData = this.calculateDelta(data, lastSync);
    
    if (deltaData && Object.keys(deltaData).length > 0) {
      this.sendToUser(userId, {
        type: 'incremental_update',
        timestamp: now,
        data: deltaData
      });
      
      this.lastSyncTime.set(userId, now);
    }
  }
  
  private calculateDelta(currentData: any, lastSyncTime: number): any {
    // 计算数据变化量
    return Object.keys(currentData).reduce((delta, key) => {
      const item = currentData[key];
      if (item.lastModified > lastSyncTime) {
        delta[key] = item;
      }
      return delta;
    }, {});
  }
}
```

**同步特性**：
- **增量更新**：只传输变化的数据，减少带宽消耗
- **数据压缩**：60-80%的数据压缩率
- **冲突解决**：基于时间戳的冲突解决机制
- **离线同步**：支持离线用户的数据同步

## 房间管理系统

### 1. 协作房间架构

#### 1.1 房间实体设计
```typescript
class CollaborationRoom {
  projectId: string;
  sceneId: string;
  users: Map<string, CollaborationUser>;
  clients: Map<string, WebSocket>;
  operations: Operation[];
  partitions: Map<string, RoomPartition>;
  
  constructor(projectId: string, sceneId: string, config?: CollaborationRoomConfig) {
    this.config = {
      maxOperationHistory: 1000,
      maxUsers: 50,
      enablePartitioning: true,
      enableResourceMonitoring: true,
      enableOperationCompression: true,
      enableIncrementalSync: true,
      ...config
    };
    
    // 启动资源监控
    if (this.config.enableResourceMonitoring) {
      this.startResourceMonitoring();
    }
  }
}
```

**房间功能**：
- **用户管理**：支持最多50个并发用户
- **操作历史**：记录和回放用户操作
- **分区管理**：基于空间的智能分区
- **资源监控**：实时监控房间资源使用情况

#### 1.2 分区优化
```typescript
interface RoomPartition {
  id: string;
  type: 'area' | 'component' | 'custom';
  bounds?: {
    minX: number; minY: number; minZ: number;
    maxX: number; maxY: number; maxZ: number;
  };
  users: Set<string>;
  operations: Operation[];
  metadata: Record<string, any>;
}
```

**分区特性**：
- **空间分区**：基于3D空间的智能分区
- **组件分区**：基于功能组件的逻辑分区
- **动态调整**：根据用户分布动态调整分区
- **局部同步**：只同步相关分区的数据

## 性能优化与监控

### 1. 性能监控系统

#### 1.1 实时指标收集
```typescript
class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  
  collectMetrics(): void {
    const cpuUsage = process.cpuUsage();
    const memoryUsage = process.memoryUsage();
    const networkStats = this.getNetworkStats();
    
    this.metrics.set('cpu', {
      value: cpuUsage.user + cpuUsage.system,
      timestamp: Date.now(),
      unit: 'microseconds'
    });
    
    this.metrics.set('memory', {
      value: memoryUsage.heapUsed / memoryUsage.heapTotal,
      timestamp: Date.now(),
      unit: 'percentage'
    });
  }
}
```

**监控指标**：
- **系统指标**：CPU、内存、网络、磁盘使用率
- **业务指标**：在线用户数、房间数量、操作频率
- **性能指标**：响应时间、吞吐量、错误率
- **网络指标**：延迟、丢包率、带宽使用

#### 1.2 自动告警机制
```typescript
class AlertManager {
  private thresholds = {
    cpuUsage: 80,
    memoryUsage: 85,
    responseTime: 1000,
    errorRate: 5
  };
  
  checkThresholds(metrics: Map<string, PerformanceMetric>): void {
    for (const [key, metric] of metrics) {
      const threshold = this.thresholds[key];
      if (threshold && metric.value > threshold) {
        this.sendAlert({
          type: 'threshold_exceeded',
          metric: key,
          value: metric.value,
          threshold,
          timestamp: Date.now()
        });
      }
    }
  }
}
```

**告警功能**：
- **阈值告警**：基于预设阈值的自动告警
- **趋势告警**：基于数据趋势的预测性告警
- **多渠道通知**：邮件、短信、Slack等多种通知方式
- **告警聚合**：避免告警风暴的智能聚合机制

### 2. 故障恢复机制

#### 2.1 健康检查
```typescript
class HealthCheckService {
  async performHealthCheck(): Promise<HealthCheckResult> {
    const checks = await Promise.allSettled([
      this.checkDatabase(),
      this.checkRedis(),
      this.checkWebRTC(),
      this.checkAgones()
    ]);
    
    return {
      status: checks.every(check => check.status === 'fulfilled') ? 'healthy' : 'unhealthy',
      checks: checks.map((check, index) => ({
        name: ['database', 'redis', 'webrtc', 'agones'][index],
        status: check.status,
        details: check.status === 'fulfilled' ? check.value : check.reason
      })),
      timestamp: new Date()
    };
  }
}
```

**健康检查功能**：
- **多层检查**：数据库、缓存、网络、服务状态检查
- **自动恢复**：检测到故障时的自动恢复机制
- **故障隔离**：隔离故障节点，防止故障扩散
- **服务降级**：在部分功能故障时的优雅降级

## 安全与合规

### 1. 网络安全

**安全措施**：
- **TLS加密**：所有网络通信使用TLS 1.3加密
- **身份认证**：JWT令牌和OAuth 2.0认证
- **访问控制**：基于角色的访问控制(RBAC)
- **防火墙规则**：严格的网络访问控制

### 2. 数据保护

**数据安全**：
- **数据加密**：静态数据和传输数据加密
- **备份策略**：多地域数据备份
- **审计日志**：完整的操作审计日志
- **隐私保护**：符合GDPR等隐私法规

## 部署与运维

### 1. 容器化部署

**部署特性**：
- **Docker镜像**：标准化的容器镜像
- **Helm Charts**：简化的Kubernetes部署
- **CI/CD流水线**：自动化构建和部署
- **蓝绿部署**：零停机时间的服务更新

### 2. 运维自动化

**自动化功能**：
- **自动扩缩容**：基于负载的自动扩缩容
- **故障自愈**：自动检测和修复常见故障
- **配置管理**：统一的配置管理和分发
- **版本管理**：灰度发布和快速回滚

## 总结

DL引擎的游戏服务器架构通过云原生技术、实时通信、智能负载均衡等先进技术，构建了一个高性能、高可用、可扩展的多人协作平台。该架构不仅满足了数字化学习应用的技术需求，还通过自动化运维大大降低了运营成本，为业务的快速发展提供了强有力的技术保障。

### 核心优势

1. **云原生架构**：基于Kubernetes和Agones的现代化架构
2. **实时通信**：WebRTC和MediaSoup提供的低延迟通信
3. **智能扩缩容**：基于负载的自动扩缩容机制
4. **高可用性**：多层故障恢复和自愈机制
5. **性能优化**：全方位的性能监控和优化
6. **安全合规**：完善的安全措施和合规保障

该游戏服务器架构为数字化学习领域提供了一个技术先进、功能完善、运维简单的多人协作解决方案。

## 技术实现细节

### 1. 服务启动流程

#### 1.1 引导程序
```typescript
// main.ts - 游戏服务器启动入口
async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // 配置微服务
  app.connectMicroservice({
    transport: Transport.TCP,
    options: {
      host: configService.get<string>('GAME_SERVER_HOST', 'localhost'),
      port: configService.get<number>('GAME_SERVER_MICROSERVICE_PORT', 3003),
    },
  });

  // 全局配置
  app.setGlobalPrefix('api');
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    transform: true,
    forbidNonWhitelisted: true,
  }));

  // 启用CORS
  app.enableCors({
    origin: configService.get<string>('CORS_ORIGIN', '*'),
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
  });

  // 启动服务
  await app.startAllMicroservices();
  const port = configService.get<number>('GAME_SERVER_PORT', 3030);
  await app.listen(port);

  console.log(`游戏服务器已启动，HTTP端口: ${port}`);
}
```

#### 1.2 模块架构
```typescript
// app.module.ts - 主模块配置
@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // 事件发射器模块
    EventEmitterModule.forRoot({
      wildcard: true,
      delimiter: '.',
      maxListeners: 20,
      verboseMemoryLeak: true,
    }),

    // 微服务客户端模块
    ClientsModule.registerAsync([
      {
        name: 'USER_SERVICE',
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('USER_SERVICE_HOST', 'localhost'),
            port: configService.get<number>('USER_SERVICE_PORT', 3001),
          },
        }),
        inject: [ConfigService],
      }
    ]),

    // 功能模块
    AgonesModule,
    InstanceModule,
    WebRTCModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
```

### 2. 网络协议设计

#### 2.1 消息协议
```typescript
// 消息类型定义
enum MessageType {
  // 连接管理
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  HEARTBEAT = 'heartbeat',

  // 用户管理
  USER_JOINED = 'user_joined',
  USER_LEFT = 'user_left',
  USER_STATUS = 'user_status',
  USER_LIST = 'user_list',

  // 场景同步
  SCENE_UPDATE = 'scene_update',
  OBJECT_TRANSFORM = 'object_transform',
  OBJECT_INTERACTION = 'object_interaction',

  // 协作操作
  OPERATION = 'operation',
  OPERATION_BATCH = 'operation_batch',
  OPERATION_UNDO = 'operation_undo',
  OPERATION_REDO = 'operation_redo',

  // WebRTC信令
  WEBRTC_OFFER = 'webrtc_offer',
  WEBRTC_ANSWER = 'webrtc_answer',
  WEBRTC_ICE_CANDIDATE = 'webrtc_ice_candidate',
}

// 消息基础结构
interface BaseMessage {
  type: MessageType;
  timestamp: number;
  userId?: string;
  sessionId?: string;
  data?: any;
}

// 场景更新消息
interface SceneUpdateMessage extends BaseMessage {
  type: MessageType.SCENE_UPDATE;
  data: {
    sceneId: string;
    entityId: string;
    componentType: string;
    componentData: any;
    operation: 'create' | 'update' | 'delete';
  };
}
```

#### 2.2 数据压缩
```typescript
class MessageSerializer {
  private useCompression: boolean;

  constructor(useCompression: boolean = true) {
    this.useCompression = useCompression;
  }

  serialize(data: any): ArrayBuffer {
    // 序列化为JSON
    const jsonString = JSON.stringify(data);
    const jsonBuffer = new TextEncoder().encode(jsonString);

    if (!this.useCompression) {
      return jsonBuffer;
    }

    // 使用LZ4压缩算法
    const compressed = this.compressLZ4(jsonBuffer);

    // 添加压缩标识头
    const result = new ArrayBuffer(compressed.byteLength + 1);
    const view = new Uint8Array(result);
    view[0] = 1; // 压缩标识
    view.set(new Uint8Array(compressed), 1);

    return result;
  }

  deserialize(buffer: ArrayBuffer): any {
    const view = new Uint8Array(buffer);
    const isCompressed = view[0] === 1;

    let jsonBuffer: ArrayBuffer;
    if (isCompressed) {
      // 解压缩
      const compressedData = buffer.slice(1);
      jsonBuffer = this.decompressLZ4(compressedData);
    } else {
      jsonBuffer = buffer;
    }

    // 反序列化JSON
    const jsonString = new TextDecoder().decode(jsonBuffer);
    return JSON.parse(jsonString);
  }
}
```

### 3. 状态同步机制

#### 3.1 操作转换算法
```typescript
class OperationalTransform {
  // 转换两个并发操作
  static transform(op1: Operation, op2: Operation): [Operation, Operation] {
    if (op1.type === 'insert' && op2.type === 'insert') {
      return this.transformInsertInsert(op1, op2);
    } else if (op1.type === 'delete' && op2.type === 'delete') {
      return this.transformDeleteDelete(op1, op2);
    } else if (op1.type === 'insert' && op2.type === 'delete') {
      return this.transformInsertDelete(op1, op2);
    } else if (op1.type === 'delete' && op2.type === 'insert') {
      const [op2Prime, op1Prime] = this.transformInsertDelete(op2, op1);
      return [op1Prime, op2Prime];
    }

    return [op1, op2];
  }

  private static transformInsertInsert(op1: InsertOperation, op2: InsertOperation): [Operation, Operation] {
    if (op1.position <= op2.position) {
      return [
        op1,
        { ...op2, position: op2.position + op1.content.length }
      ];
    } else {
      return [
        { ...op1, position: op1.position + op2.content.length },
        op2
      ];
    }
  }
}
```

#### 3.2 冲突解决
```typescript
class ConflictResolver {
  // 基于时间戳的冲突解决
  resolveConflict(localOp: Operation, remoteOp: Operation): Operation {
    if (localOp.timestamp < remoteOp.timestamp) {
      // 本地操作较早，保持不变
      return localOp;
    } else if (localOp.timestamp > remoteOp.timestamp) {
      // 远程操作较早，需要转换本地操作
      return OperationalTransform.transform(localOp, remoteOp)[0];
    } else {
      // 时间戳相同，使用用户ID作为决定因素
      if (localOp.userId < remoteOp.userId) {
        return localOp;
      } else {
        return OperationalTransform.transform(localOp, remoteOp)[0];
      }
    }
  }

  // 基于向量时钟的因果关系检测
  detectCausality(op1: Operation, op2: Operation): 'concurrent' | 'before' | 'after' {
    const vc1 = op1.vectorClock;
    const vc2 = op2.vectorClock;

    let op1BeforeOp2 = true;
    let op2BeforeOp1 = true;

    for (const userId in vc1) {
      if (vc1[userId] > (vc2[userId] || 0)) {
        op2BeforeOp1 = false;
      }
    }

    for (const userId in vc2) {
      if (vc2[userId] > (vc1[userId] || 0)) {
        op1BeforeOp2 = false;
      }
    }

    if (op1BeforeOp2 && !op2BeforeOp1) return 'before';
    if (op2BeforeOp1 && !op1BeforeOp2) return 'after';
    return 'concurrent';
  }
}
```

### 4. 资源管理优化

#### 4.1 内存池管理
```typescript
class ObjectPool<T> {
  private pool: T[] = [];
  private createFn: () => T;
  private resetFn: (obj: T) => void;
  private maxSize: number;

  constructor(createFn: () => T, resetFn: (obj: T) => void, maxSize: number = 100) {
    this.createFn = createFn;
    this.resetFn = resetFn;
    this.maxSize = maxSize;
  }

  acquire(): T {
    if (this.pool.length > 0) {
      return this.pool.pop()!;
    }
    return this.createFn();
  }

  release(obj: T): void {
    if (this.pool.length < this.maxSize) {
      this.resetFn(obj);
      this.pool.push(obj);
    }
  }

  clear(): void {
    this.pool.length = 0;
  }
}

// 消息对象池
const messagePool = new ObjectPool(
  () => ({ type: null, timestamp: 0, data: null }),
  (msg) => { msg.type = null; msg.timestamp = 0; msg.data = null; },
  1000
);
```

#### 4.2 连接池管理
```typescript
class ConnectionPool {
  private connections: Map<string, WebSocket[]> = new Map();
  private maxConnectionsPerUser: number = 5;

  addConnection(userId: string, connection: WebSocket): void {
    if (!this.connections.has(userId)) {
      this.connections.set(userId, []);
    }

    const userConnections = this.connections.get(userId)!;

    // 限制每个用户的连接数
    if (userConnections.length >= this.maxConnectionsPerUser) {
      const oldestConnection = userConnections.shift();
      if (oldestConnection && oldestConnection.readyState === WebSocket.OPEN) {
        oldestConnection.close(1000, 'Connection limit exceeded');
      }
    }

    userConnections.push(connection);
  }

  removeConnection(userId: string, connection: WebSocket): void {
    const userConnections = this.connections.get(userId);
    if (userConnections) {
      const index = userConnections.indexOf(connection);
      if (index !== -1) {
        userConnections.splice(index, 1);
      }

      if (userConnections.length === 0) {
        this.connections.delete(userId);
      }
    }
  }

  getUserConnections(userId: string): WebSocket[] {
    return this.connections.get(userId) || [];
  }

  broadcastToUser(userId: string, message: any): void {
    const connections = this.getUserConnections(userId);
    const serializedMessage = JSON.stringify(message);

    connections.forEach(connection => {
      if (connection.readyState === WebSocket.OPEN) {
        connection.send(serializedMessage);
      }
    });
  }
}
```

### 5. 监控与日志

#### 5.1 性能指标收集
```typescript
class MetricsCollector {
  private metrics: Map<string, Metric[]> = new Map();
  private readonly maxMetricsPerType = 1000;

  recordMetric(type: string, value: number, tags?: Record<string, string>): void {
    if (!this.metrics.has(type)) {
      this.metrics.set(type, []);
    }

    const metricsList = this.metrics.get(type)!;
    metricsList.push({
      value,
      timestamp: Date.now(),
      tags: tags || {}
    });

    // 限制指标数量，保持最新的指标
    if (metricsList.length > this.maxMetricsPerType) {
      metricsList.splice(0, metricsList.length - this.maxMetricsPerType);
    }
  }

  getMetrics(type: string, timeRange?: { start: number; end: number }): Metric[] {
    const metrics = this.metrics.get(type) || [];

    if (!timeRange) {
      return metrics;
    }

    return metrics.filter(metric =>
      metric.timestamp >= timeRange.start && metric.timestamp <= timeRange.end
    );
  }

  calculateAverage(type: string, timeRange?: { start: number; end: number }): number {
    const metrics = this.getMetrics(type, timeRange);
    if (metrics.length === 0) return 0;

    const sum = metrics.reduce((acc, metric) => acc + metric.value, 0);
    return sum / metrics.length;
  }
}
```

#### 5.2 结构化日志
```typescript
class StructuredLogger {
  private logLevel: LogLevel;

  constructor(logLevel: LogLevel = LogLevel.INFO) {
    this.logLevel = logLevel;
  }

  log(level: LogLevel, message: string, context?: Record<string, any>): void {
    if (level < this.logLevel) {
      return;
    }

    const logEntry = {
      timestamp: new Date().toISOString(),
      level: LogLevel[level],
      message,
      context: context || {},
      service: 'game-server',
      version: process.env.APP_VERSION || 'unknown',
      instanceId: process.env.INSTANCE_ID || 'unknown',
    };

    console.log(JSON.stringify(logEntry));
  }

  info(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.INFO, message, context);
  }

  warn(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.WARN, message, context);
  }

  error(message: string, error?: Error, context?: Record<string, any>): void {
    const errorContext = {
      ...context,
      error: error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
      } : undefined,
    };

    this.log(LogLevel.ERROR, message, errorContext);
  }
}

enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}
```

### 6. 安全防护机制

#### 6.1 速率限制
```typescript
class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  private readonly windowSize: number = 60000; // 1分钟
  private readonly maxRequests: number = 100;

  isAllowed(clientId: string): boolean {
    const now = Date.now();
    const windowStart = now - this.windowSize;

    if (!this.requests.has(clientId)) {
      this.requests.set(clientId, []);
    }

    const clientRequests = this.requests.get(clientId)!;

    // 清理过期的请求记录
    while (clientRequests.length > 0 && clientRequests[0] < windowStart) {
      clientRequests.shift();
    }

    // 检查是否超过限制
    if (clientRequests.length >= this.maxRequests) {
      return false;
    }

    // 记录新请求
    clientRequests.push(now);
    return true;
  }

  getRemainingRequests(clientId: string): number {
    const clientRequests = this.requests.get(clientId) || [];
    return Math.max(0, this.maxRequests - clientRequests.length);
  }
}
```

#### 6.2 输入验证
```typescript
class InputValidator {
  static validateMessage(message: any): ValidationResult {
    const errors: string[] = [];

    // 检查必需字段
    if (!message.type) {
      errors.push('Message type is required');
    }

    if (!message.timestamp || typeof message.timestamp !== 'number') {
      errors.push('Valid timestamp is required');
    }

    // 检查消息大小
    const messageSize = JSON.stringify(message).length;
    if (messageSize > 1024 * 1024) { // 1MB限制
      errors.push('Message size exceeds limit');
    }

    // 检查特定消息类型的数据
    if (message.type === MessageType.SCENE_UPDATE) {
      if (!message.data || !message.data.sceneId) {
        errors.push('Scene ID is required for scene updates');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static sanitizeInput(input: string): string {
    // 移除潜在的恶意字符
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  }
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}
```

## 部署配置详解

### 1. Kubernetes部署配置

#### 1.1 Deployment配置
```yaml
# game-server-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: game-server
  namespace: dl-engine
  labels:
    app: game-server
    version: v1.0.0
spec:
  replicas: 3
  selector:
    matchLabels:
      app: game-server
  template:
    metadata:
      labels:
        app: game-server
        version: v1.0.0
    spec:
      containers:
      - name: game-server
        image: dl-engine/game-server:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3030
          name: http
        - containerPort: 3003
          name: microservice
        - containerPort: 10000
          name: webrtc-udp
          protocol: UDP
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        env:
        - name: NODE_ENV
          value: "production"
        - name: AGONES_ENABLED
          value: "true"
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: url
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3030
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3030
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
```

#### 1.2 Service配置
```yaml
# game-server-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: game-server-service
  namespace: dl-engine
  labels:
    app: game-server
spec:
  type: ClusterIP
  ports:
  - port: 3030
    targetPort: 3030
    protocol: TCP
    name: http
  - port: 3003
    targetPort: 3003
    protocol: TCP
    name: microservice
  selector:
    app: game-server
---
# WebRTC UDP服务
apiVersion: v1
kind: Service
metadata:
  name: game-server-webrtc
  namespace: dl-engine
spec:
  type: LoadBalancer
  ports:
  - port: 10000
    targetPort: 10000
    protocol: UDP
    name: webrtc-udp
  selector:
    app: game-server
```

#### 1.3 ConfigMap配置
```yaml
# game-server-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: game-server-config
  namespace: dl-engine
data:
  NODE_ENV: "production"
  LOG_LEVEL: "info"
  MAX_USERS_PER_INSTANCE: "50"
  INSTANCE_SHUTDOWN_DELAY_MS: "30000"
  MEDIASOUP_NUM_WORKERS: "4"
  WEBRTC_LISTEN_IP: "0.0.0.0"
  WEBRTC_ANNOUNCED_IP: "auto"
  CORS_ORIGIN: "*"
  RATE_LIMIT_WINDOW_MS: "60000"
  RATE_LIMIT_MAX_REQUESTS: "100"
```

### 2. 监控配置

#### 2.1 Prometheus配置
```yaml
# prometheus-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s

    rule_files:
      - "game-server-rules.yml"

    scrape_configs:
    - job_name: 'game-server'
      static_configs:
      - targets: ['game-server-service.dl-engine:3030']
      metrics_path: '/api/metrics'
      scrape_interval: 10s

    - job_name: 'agones-gameservers'
      kubernetes_sd_configs:
      - role: pod
        namespaces:
          names:
          - default
      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_label_agones_dev_role]
        action: keep
        regex: gameserver
```

#### 2.2 告警规则
```yaml
# game-server-alerts.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: game-server-alerts
  namespace: monitoring
data:
  game-server-rules.yml: |
    groups:
    - name: game-server
      rules:
      - alert: GameServerHighCPU
        expr: rate(process_cpu_seconds_total[5m]) * 100 > 80
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "游戏服务器CPU使用率过高"
          description: "实例 {{ $labels.instance }} CPU使用率超过80%"

      - alert: GameServerHighMemory
        expr: (process_resident_memory_bytes / 1024 / 1024) > 800
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "游戏服务器内存使用率过高"
          description: "实例 {{ $labels.instance }} 内存使用超过800MB"

      - alert: GameServerDown
        expr: up{job="game-server"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "游戏服务器实例下线"
          description: "实例 {{ $labels.instance }} 已下线超过1分钟"

      - alert: WebRTCConnectionFailure
        expr: rate(webrtc_connection_failures_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "WebRTC连接失败率过高"
          description: "WebRTC连接失败率超过10%"
```

### 3. 日志配置

#### 3.1 Fluentd配置
```yaml
# fluentd-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluentd-config
  namespace: logging
data:
  fluent.conf: |
    <source>
      @type tail
      path /var/log/containers/game-server-*.log
      pos_file /var/log/fluentd-game-server.log.pos
      tag kubernetes.game-server
      format json
      time_key timestamp
      time_format %Y-%m-%dT%H:%M:%S.%NZ
    </source>

    <filter kubernetes.game-server>
      @type kubernetes_metadata
      @id filter_kube_metadata
    </filter>

    <filter kubernetes.game-server>
      @type parser
      key_name log
      reserve_data true
      <parse>
        @type json
      </parse>
    </filter>

    <match kubernetes.game-server>
      @type elasticsearch
      host elasticsearch.logging.svc.cluster.local
      port 9200
      index_name game-server-logs
      type_name _doc
      include_tag_key true
      tag_key @log_name
      flush_interval 10s
    </match>
```

#### 2.2 ELK Stack部署
```yaml
# elasticsearch.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: elasticsearch
  namespace: logging
spec:
  serviceName: elasticsearch
  replicas: 3
  selector:
    matchLabels:
      app: elasticsearch
  template:
    metadata:
      labels:
        app: elasticsearch
    spec:
      containers:
      - name: elasticsearch
        image: docker.elastic.co/elasticsearch/elasticsearch:7.15.0
        ports:
        - containerPort: 9200
          name: rest
        - containerPort: 9300
          name: inter-node
        env:
        - name: cluster.name
          value: game-server-logs
        - name: node.name
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: discovery.seed_hosts
          value: "elasticsearch-0.elasticsearch,elasticsearch-1.elasticsearch,elasticsearch-2.elasticsearch"
        - name: cluster.initial_master_nodes
          value: "elasticsearch-0,elasticsearch-1,elasticsearch-2"
        - name: ES_JAVA_OPTS
          value: "-Xms512m -Xmx512m"
        volumeMounts:
        - name: data
          mountPath: /usr/share/elasticsearch/data
  volumeClaimTemplates:
  - metadata:
      name: data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 10Gi
```

### 4. 备份与恢复

#### 4.1 数据备份策略
```bash
#!/bin/bash
# backup-script.sh

# 配置变量
BACKUP_DIR="/backups/game-server"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=7

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份Redis数据
echo "开始备份Redis数据..."
kubectl exec -n dl-engine redis-0 -- redis-cli BGSAVE
kubectl cp dl-engine/redis-0:/data/dump.rdb $BACKUP_DIR/redis_$DATE.rdb

# 备份MySQL数据
echo "开始备份MySQL数据..."
kubectl exec -n dl-engine mysql-0 -- mysqldump -u root -p$MYSQL_ROOT_PASSWORD --all-databases > $BACKUP_DIR/mysql_$DATE.sql

# 备份Kubernetes配置
echo "开始备份Kubernetes配置..."
kubectl get all -n dl-engine -o yaml > $BACKUP_DIR/k8s_config_$DATE.yaml

# 压缩备份文件
echo "压缩备份文件..."
tar -czf $BACKUP_DIR/game_server_backup_$DATE.tar.gz -C $BACKUP_DIR redis_$DATE.rdb mysql_$DATE.sql k8s_config_$DATE.yaml

# 清理临时文件
rm $BACKUP_DIR/redis_$DATE.rdb $BACKUP_DIR/mysql_$DATE.sql $BACKUP_DIR/k8s_config_$DATE.yaml

# 清理过期备份
echo "清理过期备份..."
find $BACKUP_DIR -name "game_server_backup_*.tar.gz" -mtime +$RETENTION_DAYS -delete

echo "备份完成: $BACKUP_DIR/game_server_backup_$DATE.tar.gz"
```

#### 4.2 灾难恢复流程
```bash
#!/bin/bash
# disaster-recovery.sh

BACKUP_FILE=$1
RECOVERY_DIR="/tmp/recovery"

if [ -z "$BACKUP_FILE" ]; then
    echo "使用方法: $0 <backup_file>"
    exit 1
fi

echo "开始灾难恢复流程..."

# 创建恢复目录
mkdir -p $RECOVERY_DIR
cd $RECOVERY_DIR

# 解压备份文件
echo "解压备份文件..."
tar -xzf $BACKUP_FILE

# 恢复Redis数据
echo "恢复Redis数据..."
kubectl cp redis_*.rdb dl-engine/redis-0:/data/dump.rdb
kubectl exec -n dl-engine redis-0 -- redis-cli DEBUG RESTART

# 恢复MySQL数据
echo "恢复MySQL数据..."
kubectl exec -i -n dl-engine mysql-0 -- mysql -u root -p$MYSQL_ROOT_PASSWORD < mysql_*.sql

# 恢复Kubernetes配置
echo "恢复Kubernetes配置..."
kubectl apply -f k8s_config_*.yaml

# 重启游戏服务器
echo "重启游戏服务器..."
kubectl rollout restart deployment/game-server -n dl-engine

# 等待服务恢复
echo "等待服务恢复..."
kubectl wait --for=condition=available --timeout=300s deployment/game-server -n dl-engine

echo "灾难恢复完成"
```

### 5. 性能调优

#### 5.1 JVM调优（如果使用Java组件）
```yaml
# java-component-config.yaml
env:
- name: JAVA_OPTS
  value: >-
    -Xms1g
    -Xmx2g
    -XX:+UseG1GC
    -XX:MaxGCPauseMillis=200
    -XX:+UnlockExperimentalVMOptions
    -XX:+UseCGroupMemoryLimitForHeap
    -XX:+PrintGC
    -XX:+PrintGCDetails
    -XX:+PrintGCTimeStamps
    -Djava.security.egd=file:/dev/./urandom
```

#### 5.2 Node.js调优
```yaml
# nodejs-optimization.yaml
env:
- name: NODE_OPTIONS
  value: >-
    --max-old-space-size=1024
    --max-semi-space-size=64
    --optimize-for-size
    --gc-interval=100
- name: UV_THREADPOOL_SIZE
  value: "16"
- name: NODE_ENV
  value: "production"
```

#### 5.3 网络调优
```yaml
# network-optimization.yaml
spec:
  template:
    spec:
      containers:
      - name: game-server
        securityContext:
          capabilities:
            add:
            - NET_ADMIN
        env:
        - name: TCP_KEEPALIVE_TIME
          value: "600"
        - name: TCP_KEEPALIVE_INTVL
          value: "60"
        - name: TCP_KEEPALIVE_PROBES
          value: "3"
        - name: NET_CORE_RMEM_MAX
          value: "16777216"
        - name: NET_CORE_WMEM_MAX
          value: "16777216"
```

## 运维最佳实践

### 1. 健康检查策略

#### 1.1 多层健康检查
```typescript
class HealthCheckController {
  @Get('/health')
  async getHealth(): Promise<HealthStatus> {
    const checks = await Promise.allSettled([
      this.checkDatabase(),
      this.checkRedis(),
      this.checkWebRTC(),
      this.checkAgones(),
      this.checkMemory(),
      this.checkCPU(),
    ]);

    const results = checks.map((check, index) => ({
      name: ['database', 'redis', 'webrtc', 'agones', 'memory', 'cpu'][index],
      status: check.status === 'fulfilled' ? 'healthy' : 'unhealthy',
      details: check.status === 'fulfilled' ? check.value : check.reason,
    }));

    const overallStatus = results.every(r => r.status === 'healthy') ? 'healthy' : 'unhealthy';

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      checks: results,
      uptime: process.uptime(),
      version: process.env.APP_VERSION,
    };
  }
}
```

#### 1.2 自定义健康检查
```typescript
class CustomHealthIndicator {
  async checkGameServerCapacity(): Promise<HealthIndicatorResult> {
    const instances = await this.instanceService.getAllInstances();
    const totalCapacity = instances.reduce((sum, instance) => sum + instance.maxUsers, 0);
    const currentUsers = instances.reduce((sum, instance) => sum + instance.currentUsers, 0);
    const utilizationRate = currentUsers / totalCapacity;

    if (utilizationRate > 0.9) {
      return {
        status: 'unhealthy',
        message: 'Server capacity critically high',
        details: { utilizationRate, currentUsers, totalCapacity }
      };
    } else if (utilizationRate > 0.7) {
      return {
        status: 'warning',
        message: 'Server capacity high',
        details: { utilizationRate, currentUsers, totalCapacity }
      };
    }

    return {
      status: 'healthy',
      message: 'Server capacity normal',
      details: { utilizationRate, currentUsers, totalCapacity }
    };
  }
}
```

### 2. 故障排查指南

#### 2.1 常见问题诊断
```bash
#!/bin/bash
# troubleshooting.sh

echo "=== 游戏服务器故障诊断 ==="

# 检查Pod状态
echo "1. 检查Pod状态..."
kubectl get pods -n dl-engine -l app=game-server

# 检查服务状态
echo "2. 检查服务状态..."
kubectl get svc -n dl-engine

# 检查日志
echo "3. 检查最近的错误日志..."
kubectl logs -n dl-engine -l app=game-server --tail=50 | grep -i error

# 检查资源使用
echo "4. 检查资源使用情况..."
kubectl top pods -n dl-engine -l app=game-server

# 检查网络连接
echo "5. 检查网络连接..."
kubectl exec -n dl-engine deployment/game-server -- netstat -tlnp

# 检查Agones状态
echo "6. 检查Agones GameServer状态..."
kubectl get gameserver -o wide

# 检查Fleet状态
echo "7. 检查Fleet状态..."
kubectl describe fleet ir-game-server-fleet

echo "=== 诊断完成 ==="
```

#### 2.2 性能分析
```bash
#!/bin/bash
# performance-analysis.sh

NAMESPACE="dl-engine"
APP_LABEL="app=game-server"

echo "=== 性能分析报告 ==="

# CPU使用率分析
echo "1. CPU使用率分析..."
kubectl top pods -n $NAMESPACE -l $APP_LABEL --sort-by=cpu

# 内存使用率分析
echo "2. 内存使用率分析..."
kubectl top pods -n $NAMESPACE -l $APP_LABEL --sort-by=memory

# 网络连接分析
echo "3. 网络连接分析..."
for pod in $(kubectl get pods -n $NAMESPACE -l $APP_LABEL -o jsonpath='{.items[*].metadata.name}'); do
    echo "Pod: $pod"
    kubectl exec -n $NAMESPACE $pod -- ss -tuln | wc -l
done

# 响应时间分析
echo "4. 响应时间分析..."
for pod in $(kubectl get pods -n $NAMESPACE -l $APP_LABEL -o jsonpath='{.items[*].metadata.name}'); do
    echo "Pod: $pod"
    kubectl exec -n $NAMESPACE $pod -- curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:3030/api/health"
done

echo "=== 分析完成 ==="
```

### 3. 自动化运维脚本

#### 3.1 自动扩容脚本
```bash
#!/bin/bash
# auto-scaling.sh

NAMESPACE="dl-engine"
DEPLOYMENT="game-server"
MAX_REPLICAS=10
MIN_REPLICAS=2
CPU_THRESHOLD=70
MEMORY_THRESHOLD=80

# 获取当前副本数
CURRENT_REPLICAS=$(kubectl get deployment $DEPLOYMENT -n $NAMESPACE -o jsonpath='{.spec.replicas}')

# 获取平均CPU使用率
AVG_CPU=$(kubectl top pods -n $NAMESPACE -l app=game-server --no-headers | awk '{sum+=$2} END {print sum/NR}' | sed 's/m//')

# 获取平均内存使用率
AVG_MEMORY=$(kubectl top pods -n $NAMESPACE -l app=game-server --no-headers | awk '{sum+=$3} END {print sum/NR}' | sed 's/Mi//')

echo "当前副本数: $CURRENT_REPLICAS"
echo "平均CPU使用率: ${AVG_CPU}m"
echo "平均内存使用率: ${AVG_MEMORY}Mi"

# 扩容逻辑
if [ $AVG_CPU -gt $CPU_THRESHOLD ] || [ $AVG_MEMORY -gt $MEMORY_THRESHOLD ]; then
    if [ $CURRENT_REPLICAS -lt $MAX_REPLICAS ]; then
        NEW_REPLICAS=$((CURRENT_REPLICAS + 1))
        echo "触发扩容: $CURRENT_REPLICAS -> $NEW_REPLICAS"
        kubectl scale deployment $DEPLOYMENT -n $NAMESPACE --replicas=$NEW_REPLICAS
    else
        echo "已达到最大副本数限制"
    fi
# 缩容逻辑
elif [ $AVG_CPU -lt 30 ] && [ $AVG_MEMORY -lt 40 ]; then
    if [ $CURRENT_REPLICAS -gt $MIN_REPLICAS ]; then
        NEW_REPLICAS=$((CURRENT_REPLICAS - 1))
        echo "触发缩容: $CURRENT_REPLICAS -> $NEW_REPLICAS"
        kubectl scale deployment $DEPLOYMENT -n $NAMESPACE --replicas=$NEW_REPLICAS
    else
        echo "已达到最小副本数限制"
    fi
else
    echo "资源使用率正常，无需调整"
fi
```

#### 3.2 健康检查和自愈脚本
```bash
#!/bin/bash
# health-check-and-heal.sh

NAMESPACE="dl-engine"
APP_LABEL="app=game-server"

echo "开始健康检查..."

# 检查不健康的Pod
UNHEALTHY_PODS=$(kubectl get pods -n $NAMESPACE -l $APP_LABEL --field-selector=status.phase!=Running -o jsonpath='{.items[*].metadata.name}')

if [ ! -z "$UNHEALTHY_PODS" ]; then
    echo "发现不健康的Pod: $UNHEALTHY_PODS"

    for pod in $UNHEALTHY_PODS; do
        echo "重启Pod: $pod"
        kubectl delete pod $pod -n $NAMESPACE
    done
fi

# 检查响应超时的Pod
for pod in $(kubectl get pods -n $NAMESPACE -l $APP_LABEL -o jsonpath='{.items[*].metadata.name}'); do
    echo "检查Pod响应: $pod"

    # 检查健康检查端点
    if ! kubectl exec -n $NAMESPACE $pod -- curl -f -s --max-time 5 "http://localhost:3030/api/health" > /dev/null; then
        echo "Pod $pod 健康检查失败，重启中..."
        kubectl delete pod $pod -n $NAMESPACE
    fi
done

# 检查GameServer状态
UNHEALTHY_GAMESERVERS=$(kubectl get gameserver --field-selector=status.state=Unhealthy -o jsonpath='{.items[*].metadata.name}')

if [ ! -z "$UNHEALTHY_GAMESERVERS" ]; then
    echo "发现不健康的GameServer: $UNHEALTHY_GAMESERVERS"

    for gs in $UNHEALTHY_GAMESERVERS; do
        echo "删除不健康的GameServer: $gs"
        kubectl delete gameserver $gs
    done
fi

echo "健康检查完成"
```

## 架构演进与未来规划

### 1. 技术演进路线

#### 1.1 短期目标（3-6个月）
- √**性能优化**：进一步优化WebRTC传输性能，降低延迟至30ms以下
- √**扩展性增强**：支持更大规模的并发用户（单实例100+用户）
- **监控完善**：增加更细粒度的性能监控和业务指标
- **安全加固**：实施更严格的安全策略和访问控制

#### 1.2 中期目标（6-12个月）
- √**多地域部署**：支持跨地域的游戏服务器部署和数据同步
- √**AI集成**：集成AI助手和智能推荐系统
- √**边缘计算**：在边缘节点部署轻量级游戏服务器
- **服务网格**：引入Istio等服务网格技术

#### 1.3 长期目标（1-2年）
- **云原生升级**：全面拥抱云原生技术栈
- **无服务器架构**：部分功能迁移到Serverless架构
- **区块链集成**：支持NFT和数字资产管理
- **元宇宙支持**：为元宇宙应用提供基础设施

### 2. 架构优化建议

#### 2.1 性能优化
```typescript
// 连接池优化
class OptimizedConnectionPool {
  private pools: Map<string, ConnectionPool> = new Map();
  private readonly maxPoolSize = 1000;
  private readonly minPoolSize = 10;

  getConnection(userId: string): WebSocket {
    let pool = this.pools.get(userId);
    if (!pool) {
      pool = new ConnectionPool(this.minPoolSize, this.maxPoolSize);
      this.pools.set(userId, pool);
    }
    return pool.acquire();
  }

  releaseConnection(userId: string, connection: WebSocket): void {
    const pool = this.pools.get(userId);
    if (pool) {
      pool.release(connection);
    }
  }
}

// 缓存优化
class DistributedCache {
  private localCache: Map<string, any> = new Map();
  private redisClient: Redis;

  async get(key: string): Promise<any> {
    // 先查本地缓存
    if (this.localCache.has(key)) {
      return this.localCache.get(key);
    }

    // 再查Redis
    const value = await this.redisClient.get(key);
    if (value) {
      this.localCache.set(key, JSON.parse(value));
      return JSON.parse(value);
    }

    return null;
  }

  async set(key: string, value: any, ttl: number = 3600): Promise<void> {
    // 同时更新本地缓存和Redis
    this.localCache.set(key, value);
    await this.redisClient.setex(key, ttl, JSON.stringify(value));
  }
}
```

#### 2.2 可扩展性增强
```typescript
// 分片策略
class ShardingStrategy {
  private shards: Map<string, GameServerShard> = new Map();

  getShardForUser(userId: string): GameServerShard {
    const shardId = this.calculateShardId(userId);
    return this.shards.get(shardId) || this.createShard(shardId);
  }

  private calculateShardId(userId: string): string {
    // 使用一致性哈希算法
    const hash = this.consistentHash(userId);
    return `shard-${hash % this.getShardCount()}`;
  }

  private consistentHash(input: string): number {
    let hash = 0;
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }
}

// 动态负载均衡
class DynamicLoadBalancer {
  private instances: Map<string, InstanceMetrics> = new Map();

  selectOptimalInstance(requirements: InstanceRequirements): string {
    const candidates = this.filterCandidates(requirements);
    return this.selectBestCandidate(candidates);
  }

  private selectBestCandidate(candidates: InstanceMetrics[]): string {
    // 多因素评分算法
    return candidates.reduce((best, current) => {
      const bestScore = this.calculateScore(best);
      const currentScore = this.calculateScore(current);
      return currentScore > bestScore ? current : best;
    }).instanceId;
  }

  private calculateScore(metrics: InstanceMetrics): number {
    const cpuScore = (100 - metrics.cpuUsage) * 0.3;
    const memoryScore = (100 - metrics.memoryUsage) * 0.3;
    const networkScore = (100 - metrics.networkUsage) * 0.2;
    const userScore = ((metrics.maxUsers - metrics.currentUsers) / metrics.maxUsers) * 100 * 0.2;

    return cpuScore + memoryScore + networkScore + userScore;
  }
}
```

### 3. 成本优化策略

#### 3.1 资源优化
```yaml
# 资源请求优化
resources:
  requests:
    memory: "256Mi"    # 降低初始内存请求
    cpu: "200m"        # 降低初始CPU请求
  limits:
    memory: "1Gi"      # 保持合理的内存限制
    cpu: "1000m"       # 保持合理的CPU限制

# 节点亲和性配置
nodeAffinity:
  preferredDuringSchedulingIgnoredDuringExecution:
  - weight: 100
    preference:
      matchExpressions:
      - key: node-type
        operator: In
        values:
        - spot-instance    # 优先使用Spot实例
```

#### 3.2 自动化成本控制
```bash
#!/bin/bash
# cost-optimization.sh

# 检查空闲实例并自动关闭
check_idle_instances() {
    local idle_threshold=300  # 5分钟空闲时间
    local current_time=$(date +%s)

    kubectl get gameserver -o json | jq -r '.items[] | select(.status.state == "Ready") | .metadata.name' | while read gs; do
        # 获取实例最后活动时间
        last_activity=$(kubectl get gameserver $gs -o jsonpath='{.metadata.annotations.last-activity}')

        if [ ! -z "$last_activity" ]; then
            idle_time=$((current_time - last_activity))

            if [ $idle_time -gt $idle_threshold ]; then
                echo "关闭空闲实例: $gs (空闲时间: ${idle_time}秒)"
                kubectl delete gameserver $gs
            fi
        fi
    done
}

# 优化Fleet大小
optimize_fleet_size() {
    local current_hour=$(date +%H)
    local day_of_week=$(date +%u)

    # 根据时间调整Fleet大小
    if [ $current_hour -ge 22 ] || [ $current_hour -le 6 ]; then
        # 夜间时段，减少实例数
        kubectl patch fleet ir-game-server-fleet -p '{"spec":{"replicas":2}}'
    elif [ $day_of_week -ge 6 ]; then
        # 周末，适度增加实例数
        kubectl patch fleet ir-game-server-fleet -p '{"spec":{"replicas":5}}'
    else
        # 工作日，正常实例数
        kubectl patch fleet ir-game-server-fleet -p '{"spec":{"replicas":3}}'
    fi
}

check_idle_instances
optimize_fleet_size
```

### 4. 安全最佳实践

#### 4.1 网络安全策略
```yaml
# network-policy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: game-server-network-policy
  namespace: dl-engine
spec:
  podSelector:
    matchLabels:
      app: game-server
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: dl-engine
    - podSelector:
        matchLabels:
          app: api-gateway
    ports:
    - protocol: TCP
      port: 3030
  - from: []  # 允许来自任何地方的WebRTC连接
    ports:
    - protocol: UDP
      port: 10000
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: dl-engine
    ports:
    - protocol: TCP
      port: 3306  # MySQL
    - protocol: TCP
      port: 6379  # Redis
  - to: []  # 允许访问外部服务
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
```

#### 4.2 RBAC配置
```yaml
# rbac.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: game-server-sa
  namespace: dl-engine
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: dl-engine
  name: game-server-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["agones.dev"]
  resources: ["gameservers", "fleets"]
  verbs: ["get", "list", "watch", "update"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: game-server-rolebinding
  namespace: dl-engine
subjects:
- kind: ServiceAccount
  name: game-server-sa
  namespace: dl-engine
roleRef:
  kind: Role
  name: game-server-role
  apiGroup: rbac.authorization.k8s.io
```

## 总结与展望

### 架构优势总结

1. **云原生架构**：基于Kubernetes和Agones的现代化容器编排，提供了出色的可扩展性和可维护性
2. **实时通信能力**：WebRTC和MediaSoup的集成实现了低延迟、高质量的多媒体通信
3. **智能负载均衡**：多因素负载均衡算法确保了资源的最优利用
4. **自动化运维**：完善的监控、告警和自愈机制大大降低了运维成本
5. **高可用设计**：多层故障恢复机制保证了系统的稳定性
6. **安全合规**：全面的安全措施和合规保障

### 技术创新点

1. **混合架构**：结合了传统微服务和游戏服务器的优势
2. **动态扩缩容**：基于业务负载的智能扩缩容策略
3. **状态同步优化**：增量同步和操作转换算法的应用
4. **多地域支持**：为全球化部署提供了技术基础
5. **边缘计算集成**：支持边缘节点的轻量级部署

### 业务价值

1. **用户体验提升**：低延迟、高质量的实时协作体验
2. **成本效益**：自动化运维和资源优化降低了总体拥有成本
3. **快速迭代**：云原生架构支持快速功能迭代和部署
4. **全球化支持**：多地域部署能力支持业务全球化扩展
5. **技术前瞻性**：为未来的技术演进预留了充分空间

### 发展前景

DL引擎的游戏服务器架构代表了数字化学习领域的技术前沿，通过云原生技术、实时通信、人工智能等先进技术的深度融合，为教育行业的数字化转型提供了强有力的技术支撑。

随着5G、边缘计算、AI等技术的不断发展，该架构将继续演进，为更加丰富、沉浸式的学习体验提供技术保障，推动教育行业向更加智能化、个性化的方向发展。

## 参考资料

### 技术文档
- [Kubernetes官方文档](https://kubernetes.io/docs/)
- [Agones游戏服务器编排](https://agones.dev/site/)
- [MediaSoup实时通信](https://mediasoup.org/)
- [WebRTC标准规范](https://webrtc.org/)
- [NestJS框架文档](https://nestjs.com/)

### 最佳实践
- [云原生应用架构指南](https://12factor.net/)
- [微服务架构模式](https://microservices.io/)
- [Kubernetes生产环境最佳实践](https://kubernetes.io/docs/setup/best-practices/)
- [游戏服务器架构设计](https://www.gamedev.net/articles/programming/networking-and-multiplayer/)

### 监控与运维
- [Prometheus监控指南](https://prometheus.io/docs/)
- [Grafana可视化](https://grafana.com/docs/)
- [ELK日志分析](https://www.elastic.co/guide/)
- [Kubernetes运维实践](https://kubernetes.io/docs/tasks/)
```

# DL引擎RAG应用技术实现方案

## 实施概述

本文档详细描述了RAG应用系统的技术实现方案，包括各个组件的具体实现细节、代码结构和集成方式。

## 实施阶段

### 阶段一：知识库服务实现 (2周)
1. 创建知识库微服务
2. 实现文档上传和解析
3. 集成向量数据库
4. 实现语义检索功能

### 阶段二：数字人系统增强 (2周)
1. 扩展Avatar系统
2. 集成语音识别和合成
3. 实现嘴形同步
4. 添加情感表达

### 阶段三：RAG对话引擎 (2周)
1. 创建对话服务
2. 实现意图理解
3. 集成知识检索
4. 实现回答生成

### 阶段四：编辑器界面开发 (1周)
1. 知识库管理界面
2. 数字人配置界面
3. RAG应用创建界面

### 阶段五：系统集成和测试 (1周)
1. 端到端集成
2. 功能测试
3. 性能优化
4. 部署配置

## 核心组件实现

### 1. 知识库服务

#### 服务结构
```
server/knowledge-base-service/
├── src/
│   ├── knowledge-base/
│   │   ├── knowledge-base.controller.ts
│   │   ├── knowledge-base.service.ts
│   │   ├── knowledge-base.entity.ts
│   │   └── dto/
│   ├── documents/
│   │   ├── documents.controller.ts
│   │   ├── documents.service.ts
│   │   ├── document.entity.ts
│   │   └── processors/
│   ├── vector-store/
│   │   ├── vector-store.service.ts
│   │   ├── embeddings.service.ts
│   │   └── search.service.ts
│   └── main.ts
├── package.json
└── Dockerfile
```

#### 核心实现
```typescript
// knowledge-base.service.ts
@Injectable()
export class KnowledgeBaseService {
  constructor(
    @InjectRepository(KnowledgeBase)
    private knowledgeBaseRepository: Repository<KnowledgeBase>,
    private vectorStoreService: VectorStoreService,
    private documentsService: DocumentsService,
  ) {}

  async create(createDto: CreateKnowledgeBaseDto): Promise<KnowledgeBase> {
    const knowledgeBase = this.knowledgeBaseRepository.create({
      ...createDto,
      status: 'active',
      createdAt: new Date(),
    });
    
    const saved = await this.knowledgeBaseRepository.save(knowledgeBase);
    
    // 初始化向量存储
    await this.vectorStoreService.createCollection(saved.id);
    
    return saved;
  }

  async uploadDocument(
    knowledgeBaseId: string,
    file: Express.Multer.File,
    metadata: DocumentMetadata,
  ): Promise<Document> {
    // 处理文档
    const processedContent = await this.documentsService.processDocument(file);
    
    // 创建文档记录
    const document = await this.documentsService.create({
      knowledgeBaseId,
      filename: file.originalname,
      content: processedContent.text,
      metadata: {
        ...metadata,
        fileSize: file.size,
        mimeType: file.mimetype,
      },
    });

    // 生成向量嵌入
    await this.vectorStoreService.addDocument(
      knowledgeBaseId,
      document.id,
      processedContent.chunks,
    );

    return document;
  }

  async search(
    knowledgeBaseId: string,
    query: string,
    options: SearchOptions = {},
  ): Promise<SearchResult[]> {
    return this.vectorStoreService.search(knowledgeBaseId, query, options);
  }
}
```

#### 文档处理器
```typescript
// processors/document-processor.ts
@Injectable()
export class DocumentProcessor {
  async processDocument(file: Express.Multer.File): Promise<ProcessedDocument> {
    const extension = path.extname(file.originalname).toLowerCase();
    
    switch (extension) {
      case '.pdf':
        return this.processPDF(file);
      case '.docx':
        return this.processDocx(file);
      case '.pptx':
        return this.processPPTX(file);
      case '.xlsx':
        return this.processExcel(file);
      case '.txt':
        return this.processText(file);
      default:
        throw new Error(`不支持的文件类型: ${extension}`);
    }
  }

  private async processPDF(file: Express.Multer.File): Promise<ProcessedDocument> {
    const pdfParse = require('pdf-parse');
    const buffer = fs.readFileSync(file.path);
    const data = await pdfParse(buffer);
    
    return {
      text: data.text,
      chunks: this.chunkText(data.text),
      metadata: {
        pages: data.numpages,
        info: data.info,
      },
    };
  }

  private chunkText(text: string, chunkSize: number = 1000): TextChunk[] {
    const sentences = text.split(/[。！？.!?]/);
    const chunks: TextChunk[] = [];
    let currentChunk = '';
    let chunkIndex = 0;

    for (const sentence of sentences) {
      if (currentChunk.length + sentence.length > chunkSize) {
        if (currentChunk.trim()) {
          chunks.push({
            id: `chunk_${chunkIndex++}`,
            text: currentChunk.trim(),
            startIndex: text.indexOf(currentChunk),
            endIndex: text.indexOf(currentChunk) + currentChunk.length,
          });
        }
        currentChunk = sentence;
      } else {
        currentChunk += sentence + '。';
      }
    }

    if (currentChunk.trim()) {
      chunks.push({
        id: `chunk_${chunkIndex}`,
        text: currentChunk.trim(),
        startIndex: text.lastIndexOf(currentChunk),
        endIndex: text.lastIndexOf(currentChunk) + currentChunk.length,
      });
    }

    return chunks;
  }
}
```

### 2. 向量存储服务

```typescript
// vector-store.service.ts
@Injectable()
export class VectorStoreService {
  private chromaClient: ChromaApi;
  private embeddingsService: EmbeddingsService;

  constructor() {
    this.chromaClient = new ChromaApi({
      path: process.env.CHROMA_URL || 'http://localhost:8000',
    });
    this.embeddingsService = new EmbeddingsService();
  }

  async createCollection(knowledgeBaseId: string): Promise<void> {
    await this.chromaClient.createCollection({
      name: `kb_${knowledgeBaseId}`,
      metadata: {
        description: `Knowledge base ${knowledgeBaseId}`,
        created_at: new Date().toISOString(),
      },
    });
  }

  async addDocument(
    knowledgeBaseId: string,
    documentId: string,
    chunks: TextChunk[],
  ): Promise<void> {
    const collection = await this.chromaClient.getCollection({
      name: `kb_${knowledgeBaseId}`,
    });

    for (const chunk of chunks) {
      const embedding = await this.embeddingsService.generateEmbedding(chunk.text);
      
      await collection.add({
        ids: [`${documentId}_${chunk.id}`],
        embeddings: [embedding],
        documents: [chunk.text],
        metadatas: [{
          documentId,
          chunkId: chunk.id,
          startIndex: chunk.startIndex,
          endIndex: chunk.endIndex,
        }],
      });
    }
  }

  async search(
    knowledgeBaseId: string,
    query: string,
    options: SearchOptions = {},
  ): Promise<SearchResult[]> {
    const collection = await this.chromaClient.getCollection({
      name: `kb_${knowledgeBaseId}`,
    });

    const queryEmbedding = await this.embeddingsService.generateEmbedding(query);
    
    const results = await collection.query({
      queryEmbeddings: [queryEmbedding],
      nResults: options.topK || 5,
      where: options.filter,
    });

    return results.documents[0].map((doc, index) => ({
      id: results.ids[0][index],
      content: doc,
      score: results.distances[0][index],
      metadata: results.metadatas[0][index],
    }));
  }
}
```

### 3. RAG对话引擎

```typescript
// rag-dialogue.service.ts
@Injectable()
export class RAGDialogueService {
  constructor(
    private knowledgeBaseService: KnowledgeBaseService,
    private llmService: LLMService,
    private intentService: IntentService,
  ) {}

  async processMessage(
    sessionId: string,
    message: string,
    context: DialogueContext,
  ): Promise<DialogueResponse> {
    // 1. 意图理解
    const intent = await this.intentService.analyzeIntent(message);
    
    // 2. 知识检索
    const searchResults = await this.knowledgeBaseService.search(
      context.knowledgeBaseId,
      message,
      { topK: 3, threshold: 0.7 },
    );

    // 3. 构建提示词
    const prompt = this.buildPrompt(message, searchResults, context);

    // 4. 生成回答
    const response = await this.llmService.generateResponse(prompt, {
      maxTokens: 500,
      temperature: 0.7,
    });

    // 5. 更新对话上下文
    await this.updateContext(sessionId, message, response.text);

    return {
      text: response.text,
      intent: intent.type,
      confidence: intent.confidence,
      sources: searchResults.map(r => r.metadata),
      emotion: this.analyzeEmotion(response.text),
    };
  }

  private buildPrompt(
    query: string,
    searchResults: SearchResult[],
    context: DialogueContext,
  ): string {
    const knowledgeContext = searchResults
      .map(r => r.content)
      .join('\n\n');

    return `
你是一个专业的医疗展厅数字人助手，请基于以下知识库内容回答用户问题。

知识库内容：
${knowledgeContext}

对话历史：
${context.history.slice(-3).map(h => `${h.role}: ${h.content}`).join('\n')}

用户问题：${query}

请用友好、专业的语气回答，如果知识库中没有相关信息，请诚实地说明。
回答：`;
  }

  private analyzeEmotion(text: string): EmotionResult {
    // 简单的情感分析实现
    const positiveWords = ['好', '棒', '优秀', '健康', '安全'];
    const negativeWords = ['坏', '差', '危险', '疾病', '痛苦'];
    
    const positiveCount = positiveWords.filter(word => text.includes(word)).length;
    const negativeCount = negativeWords.filter(word => text.includes(word)).length;
    
    if (positiveCount > negativeCount) {
      return { emotion: 'positive', intensity: 0.7 };
    } else if (negativeCount > positiveCount) {
      return { emotion: 'negative', intensity: 0.6 };
    } else {
      return { emotion: 'neutral', intensity: 0.5 };
    }
  }
}
```

### 4. 数字人系统增强

```typescript
// enhanced-avatar.component.ts
export class EnhancedAvatarComponent extends AvatarComponent {
  private ragDialogueService: RAGDialogueService;
  private voiceService: VoiceService;
  private lipSyncSystem: EnhancedLipSyncSystem;
  private emotionSystem: EmotionResponseSystem;

  constructor(entity: Entity, config: EnhancedAvatarConfig) {
    super(entity, config);
    
    this.ragDialogueService = new RAGDialogueService();
    this.voiceService = new VoiceService();
    this.lipSyncSystem = new EnhancedLipSyncSystem();
    this.emotionSystem = new EmotionResponseSystem();
    
    this.knowledgeBaseId = config.knowledgeBaseId;
    this.voiceConfig = config.voice || {};
  }

  async startVoiceInteraction(): Promise<void> {
    // 开始语音识别
    this.voiceService.startRecognition({
      language: 'zh-CN',
      continuous: true,
      onResult: this.handleVoiceInput.bind(this),
      onError: this.handleVoiceError.bind(this),
    });
  }

  private async handleVoiceInput(transcript: string): Promise<void> {
    try {
      // 显示思考表情
      this.emotionSystem.setEmotion('thinking', 0.8);

      // 处理对话
      const response = await this.ragDialogueService.processMessage(
        this.sessionId,
        transcript,
        {
          knowledgeBaseId: this.knowledgeBaseId,
          avatarId: this.entity.id,
          history: this.conversationHistory,
        },
      );

      // 设置情感表情
      this.emotionSystem.setEmotion(response.emotion.emotion, response.emotion.intensity);

      // 语音合成和播放
      await this.speakResponse(response.text);

    } catch (error) {
      console.error('处理语音输入失败:', error);
      await this.speakResponse('抱歉，我没有理解您的问题，请再说一遍。');
    }
  }

  private async speakResponse(text: string): Promise<void> {
    // 生成语音
    const audioData = await this.voiceService.synthesize(text, {
      voice: this.voiceConfig.voice || 'zh-CN-XiaoxiaoNeural',
      rate: this.voiceConfig.rate || 1.0,
      pitch: this.voiceConfig.pitch || 1.0,
    });

    // 开始嘴形同步
    this.lipSyncSystem.startLipSync(audioData);

    // 播放音频
    await this.voiceService.playAudio(audioData);

    // 停止嘴形同步
    this.lipSyncSystem.stopLipSync();
  }

  setKnowledgeBase(knowledgeBaseId: string): void {
    this.knowledgeBaseId = knowledgeBaseId;
  }

  getConversationHistory(): ConversationMessage[] {
    return this.conversationHistory;
  }

  clearConversationHistory(): void {
    this.conversationHistory = [];
  }
}
```

### 5. 语音服务

```typescript
// voice.service.ts
@Injectable()
export class VoiceService {
  private speechRecognition: SpeechRecognition;
  private speechSynthesis: SpeechSynthesis;
  private audioContext: AudioContext;

  constructor() {
    this.initializeSpeechRecognition();
    this.initializeSpeechSynthesis();
    this.audioContext = new AudioContext();
  }

  startRecognition(options: RecognitionOptions): void {
    this.speechRecognition.lang = options.language || 'zh-CN';
    this.speechRecognition.continuous = options.continuous || false;
    this.speechRecognition.interimResults = options.interimResults || false;

    this.speechRecognition.onresult = (event) => {
      const result = event.results[event.results.length - 1];
      if (result.isFinal) {
        options.onResult?.(result[0].transcript);
      }
    };

    this.speechRecognition.onerror = (event) => {
      options.onError?.(event.error);
    };

    this.speechRecognition.start();
  }

  stopRecognition(): void {
    this.speechRecognition.stop();
  }

  async synthesize(text: string, options: SynthesisOptions): Promise<ArrayBuffer> {
    // 使用Azure Speech Service或其他TTS服务
    const response = await fetch('/api/voice/synthesize', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text,
        voice: options.voice,
        rate: options.rate,
        pitch: options.pitch,
      }),
    });

    return response.arrayBuffer();
  }

  async playAudio(audioData: ArrayBuffer): Promise<void> {
    const audioBuffer = await this.audioContext.decodeAudioData(audioData);
    const source = this.audioContext.createBufferSource();
    source.buffer = audioBuffer;
    source.connect(this.audioContext.destination);
    
    return new Promise((resolve) => {
      source.onended = () => resolve();
      source.start();
    });
  }

  private initializeSpeechRecognition(): void {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (SpeechRecognition) {
      this.speechRecognition = new SpeechRecognition();
    } else {
      console.warn('浏览器不支持语音识别');
    }
  }

  private initializeSpeechSynthesis(): void {
    this.speechSynthesis = window.speechSynthesis;
  }
}
```

## 数据库设计

### 知识库相关表
```sql
-- 知识库表
CREATE TABLE knowledge_bases (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  scene_id VARCHAR(36),
  owner_id VARCHAR(36) NOT NULL,
  status ENUM('active', 'inactive') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_scene_id (scene_id),
  INDEX idx_owner_id (owner_id)
);

-- 文档表
CREATE TABLE documents (
  id VARCHAR(36) PRIMARY KEY,
  knowledge_base_id VARCHAR(36) NOT NULL,
  filename VARCHAR(255) NOT NULL,
  file_path VARCHAR(500),
  file_size BIGINT,
  mime_type VARCHAR(100),
  content LONGTEXT,
  metadata JSON,
  status ENUM('processing', 'completed', 'failed') DEFAULT 'processing',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (knowledge_base_id) REFERENCES knowledge_bases(id) ON DELETE CASCADE,
  INDEX idx_knowledge_base_id (knowledge_base_id)
);

-- 对话会话表
CREATE TABLE dialogue_sessions (
  id VARCHAR(36) PRIMARY KEY,
  scene_id VARCHAR(36) NOT NULL,
  avatar_id VARCHAR(36) NOT NULL,
  knowledge_base_id VARCHAR(36) NOT NULL,
  user_id VARCHAR(36),
  status ENUM('active', 'ended') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  ended_at TIMESTAMP NULL,
  FOREIGN KEY (knowledge_base_id) REFERENCES knowledge_bases(id),
  INDEX idx_scene_id (scene_id),
  INDEX idx_user_id (user_id)
);

-- 对话消息表
CREATE TABLE dialogue_messages (
  id VARCHAR(36) PRIMARY KEY,
  session_id VARCHAR(36) NOT NULL,
  role ENUM('user', 'assistant') NOT NULL,
  content TEXT NOT NULL,
  audio_url VARCHAR(500),
  metadata JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES dialogue_sessions(id) ON DELETE CASCADE,
  INDEX idx_session_id (session_id)
);
```

## 部署配置

### Docker配置
```dockerfile
# knowledge-base-service/Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY dist ./dist

EXPOSE 3008

CMD ["node", "dist/main.js"]
```

### Kubernetes配置
```yaml
# rag-services.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: rag-config
data:
  CHROMA_URL: "http://chroma-service:8000"
  OPENAI_API_KEY: "${OPENAI_API_KEY}"
  AZURE_SPEECH_KEY: "${AZURE_SPEECH_KEY}"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: knowledge-base-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: knowledge-base-service
  template:
    metadata:
      labels:
        app: knowledge-base-service
    spec:
      containers:
      - name: knowledge-base-service
        image: dl-engine/knowledge-base-service:latest
        ports:
        - containerPort: 3008
        envFrom:
        - configMapRef:
            name: rag-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
```

## 测试策略

### 单元测试
- 知识库服务功能测试
- 文档处理器测试
- 向量检索测试
- RAG对话逻辑测试

### 集成测试
- 端到端对话流程测试
- 语音识别和合成测试
- 数字人动画同步测试

### 性能测试
- 并发用户对话测试
- 知识库检索性能测试
- 语音处理延迟测试

## 总结

本技术实现方案提供了完整的RAG应用系统实现指导，涵盖了从后端服务到前端界面的所有关键组件。通过分阶段实施，可以确保系统的稳定性和可维护性。

/**
 * DL引擎区块链模块
 * 提供Web3集成、NFT管理、数字资产处理等功能
 */

// 核心模块
export * from './core/BlockchainManager';
export * from './core/WalletManager';
export * from './core/ContractManager';

// 钱包相关
export * from './wallet/WalletAdapter';
export * from './wallet/MetaMaskAdapter';
export * from './wallet/WalletConnectAdapter';
export * from './wallet/CoinbaseWalletAdapter';

// NFT相关
export * from './nft/NFTManager';
export * from './nft/NFTRenderer';
export * from './nft/NFTMetadata';
export * from './nft/NFTMinter';

// 数字资产
export * from './assets/DigitalAssetManager';
export * from './assets/AssetTokenizer';
export * from './assets/AssetMarketplace';

// 智能合约
export * from './contracts/ContractInteractor';
export * from './contracts/NFTContract';
export * from './contracts/MarketplaceContract';
export * from './contracts/TokenContract';

// 存储
export * from './storage/IPFSManager';
export * from './storage/MetadataStorage';

// 工具类
export * from './utils/Web3Utils';
export * from './utils/BlockchainConstants';
export * from './utils/GasOptimizer';

// 类型定义
export * from './types/BlockchainTypes';
export * from './types/NFTTypes';
export * from './types/WalletTypes';
export * from './types/ContractTypes';

// 组件
export * from './components/BlockchainComponent';
export * from './components/NFTComponent';
export * from './components/WalletComponent';

// 系统
export * from './systems/BlockchainSystem';
export * from './systems/NFTSystem';
export * from './systems/AssetTokenizationSystem';

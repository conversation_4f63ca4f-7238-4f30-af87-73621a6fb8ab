/**
 * DL（Digital Learning）引擎入口文件
 * 导出所有公共API
 */

// 核心模块
export * from './core/Engine';
export * from './core/World';
export * from './core/Entity';
export * from './core/Component';
export * from './core/System';

// 渲染模块
export * from './rendering/Renderer';
export * from './rendering/Camera';
export * from './rendering/RenderSystem';
export * from './rendering/materials/index';
export * from './rendering/Light';

// 场景模块
export * from './scene/Scene';
export * from './scene/SceneManager';
export * from './scene/Transform';
export * from './scene/Skybox';

// 物理模块
export * from './physics/PhysicsSystem';
export * from './physics/PhysicsRaycastResult';
export * from './physics/character/CharacterController';
export * from './physics/ccd/ContinuousCollisionDetection';
export * from './physics/debug/PhysicsDebugger';
export * from './physics/debug/EnhancedPhysicsDebugger';
// 软体物理模块
export * from './physics/softbody/SoftBodySystem';
export * from './physics/softbody/SoftBodyComponent';
export * from './physics/constraints/SliderConstraint';
export * from './physics/constraints/FixedConstraint';
export * from './physics/constraints/WheelConstraint';

// 导出物理体相关，解决命名冲突
import type { PhysicsBody } from './physics/PhysicsBody';
import type { BodyType, PhysicsBodyOptions } from './physics/PhysicsBody';
export { PhysicsBody };
export type { BodyType, PhysicsBodyOptions };

// 导出物理碰撞体相关，解决命名冲突
import { PhysicsCollider } from './physics/PhysicsCollider';
import type { ColliderType as PhysicsColliderType, ColliderOptions as PhysicsColliderOptions } from './physics/PhysicsCollider';
export { PhysicsCollider };
export type { PhysicsColliderType, PhysicsColliderOptions };

// 导出物理组件，解决命名冲突
export * from './physics/components/CharacterControllerComponent';
export * from './physics/components/PhysicsConstraintComponent';
export * from './physics/components/PhysicsWorldComponent';

// 单独导出物理体组件和碰撞体组件，避免命名冲突
import { PhysicsBodyComponent } from './physics/components/PhysicsBodyComponent';
import { PhysicsColliderComponent } from './physics/components/PhysicsColliderComponent';
export { PhysicsBodyComponent, PhysicsColliderComponent };

// 粒子模块
export * from './particles/ParticleSystem';
export * from './particles/ParticleEmitter';
export * from './particles/Particle';

// 资产模块
export * from './assets/AssetManager';
export * from './assets/AssetLoader';
export * from './assets/ResourceManager';

// GLTF模块
export * from './gltf';

// 数学模块
// 以下模块暂未实现，使用 three.js 的数学库代替
// export * from './math/Vector2';
// export * from './math/Vector3';
// export * from './math/Quaternion';
// export * from './math/Matrix4';

// 动画模块
import type { AnimationClip } from './animation/AnimationClip';
import { Animator } from './animation/Animator';
import { AnimationSystem } from './animation/AnimationSystem';
import { BlendSpace1D } from './animation/BlendSpace1D';
import { BlendSpace2D } from './animation/BlendSpace2D';
import { AnimationStateMachine } from './animation/AnimationStateMachine';

// 导出动画系统类
export {
  AnimationClip,
  Animator,
  AnimationSystem,
  BlendSpace1D,
  BlendSpace2D,
  AnimationStateMachine
};

// 重命名可能冲突的类型
import type { AnimationState, AnimationEventType } from './animation/Animator';
export type { AnimationState as AnimatorState, AnimationEventType as AnimatorEventType };

// UI模块
export * from './ui/UIModule';

// 输入模块
export * from './input/InputSystem';
export * from './input/InputManager';
export * from './input/InputDevice';
export * from './input/InputAction';
export * from './input/InputBinding';
export * from './input/InputMapping';
export * from './input/InputRecorder';
export * from './input/InputVisualizer';
export * from './input/components/InputComponent';
export * from './input/devices/KeyboardDevice';
export * from './input/devices/MouseDevice';
export * from './input/devices/GamepadDevice';
export * from './input/devices/TouchDevice';
export * from './input/devices/XRDevice';

// 交互模块
export * from './interaction';

// 音频模块
export * from './audio/AudioSystem';
export * from './audio/AudioSource';
export * from './audio/AudioListener';

// 网络模块
export * from './network/NetworkSystem';
export * from './network/NetworkManager';
export * from './network/NetworkConnection';
export * from './network/WebSocketConnection';
export * from './network/WebRTCConnection';
export * from './network/NetworkMessage';
export * from './network/NetworkEvent';
export * from './network/NetworkEntity';
export * from './network/NetworkUser';
export * from './network/MessageType';
export * from './network/MessageSerializer';
export * from './network/components/NetworkEntityComponent';
export * from './network/components/NetworkTransformComponent';
export * from './network/components/NetworkUserComponent';

// 工具模块
export * from './utils/EventEmitter';
export * from './utils/Time';
export * from './utils/Debug';
export * from './utils/UUID';

// 角色模块
export * from './avatar/controllers';

// 国际化模块
export * from './i18n/I18n';

// 视觉脚本模块
export * from './visualscript';

// 区块链模块
export * from './blockchain';

/**
 * NFT组件 - 为实体添加NFT相关功能和数据
 */

import { Component } from '../../core/Component';
import { 
  NFTToken, 
  NFTMetadata, 
  NFTRenderOptions,
  NFTDisplayConfig,
  AssetType,
  LicenseType,
  InteractionType
} from '../types/NFTTypes';

export class NFTComponent extends Component {
  public readonly type = 'NFTComponent';
  
  // NFT基本信息
  private nftToken: NFTToken;
  
  // 渲染配置
  private renderOptions: NFTRenderOptions;
  private displayConfig: NFTDisplayConfig;
  
  // 状态
  private isLoaded: boolean = false;
  private isDisplayed: boolean = false;
  private isInteractive: boolean = false;
  
  // 渲染对象引用
  private renderObject: any = null;
  private boundingBox: any = null;
  
  // 交互状态
  private isHovered: boolean = false;
  private isSelected: boolean = false;
  private lastInteractionTime: number = 0;
  
  // 动画状态
  private animations: Map<string, any> = new Map();
  private currentAnimation: string | null = null;
  
  // 性能统计
  private loadTime: number = 0;
  private renderTime: number = 0;
  private interactionCount: number = 0;

  constructor(nftToken: NFTToken, renderOptions?: NFTRenderOptions, displayConfig?: NFTDisplayConfig) {
    super();
    
    this.nftToken = nftToken;
    
    this.renderOptions = {
      quality: 'medium',
      enableAnimations: true,
      enableInteractions: true,
      enablePhysics: false,
      enableAudio: true,
      maxTextureSize: 1024,
      lodLevel: 1,
      renderDistance: 100,
      ...renderOptions
    };
    
    this.displayConfig = {
      showMetadata: true,
      showOwnership: true,
      showPricing: false,
      showHistory: false,
      enableFullscreen: true,
      enableSharing: true,
      enableDownload: false,
      watermarkEnabled: true,
      ...displayConfig
    };
  }

  /**
   * 组件初始化
   */
  initialize(): void {
    console.log('初始化NFT组件:', this.nftToken.tokenId);
    
    // 设置交互性
    this.isInteractive = this.renderOptions.enableInteractions && 
                        this.hasInteractionCapabilities();
    
    // 开始加载计时
    this.loadTime = Date.now();
  }

  /**
   * 组件更新
   */
  update(): void {
    // 更新动画
    this.updateAnimations();
    
    // 更新交互状态
    this.updateInteractionState();
    
    // 更新渲染对象
    this.updateRenderObject();
  }

  /**
   * 获取NFT令牌信息
   */
  getNFTToken(): NFTToken {
    return { ...this.nftToken };
  }

  /**
   * 获取NFT元数据
   */
  getMetadata(): NFTMetadata {
    return this.nftToken.metadata;
  }

  /**
   * 获取资产类型
   */
  getAssetType(): AssetType {
    return this.nftToken.metadata.dl_engine_data.asset_type;
  }

  /**
   * 获取许可证类型
   */
  getLicenseType(): LicenseType {
    return this.nftToken.metadata.dl_engine_data.license_type;
  }

  /**
   * 设置渲染对象
   */
  setRenderObject(renderObject: any): void {
    this.renderObject = renderObject;
    this.isDisplayed = true;
    
    // 计算渲染时间
    if (this.loadTime > 0) {
      this.renderTime = Date.now() - this.loadTime;
    }
    
    // 设置边界框
    this.updateBoundingBox();
  }

  /**
   * 获取渲染对象
   */
  getRenderObject(): any {
    return this.renderObject;
  }

  /**
   * 设置加载状态
   */
  setLoaded(loaded: boolean): void {
    this.isLoaded = loaded;
    
    if (loaded && this.loadTime > 0) {
      console.log(`NFT ${this.nftToken.tokenId} 加载完成，耗时: ${Date.now() - this.loadTime}ms`);
    }
  }

  /**
   * 检查是否已加载
   */
  isNFTLoaded(): boolean {
    return this.isLoaded;
  }

  /**
   * 检查是否已显示
   */
  isNFTDisplayed(): boolean {
    return this.isDisplayed;
  }

  /**
   * 设置悬停状态
   */
  setHovered(hovered: boolean): void {
    if (this.isHovered !== hovered) {
      this.isHovered = hovered;
      this.onHoverStateChanged(hovered);
    }
  }

  /**
   * 设置选中状态
   */
  setSelected(selected: boolean): void {
    if (this.isSelected !== selected) {
      this.isSelected = selected;
      this.onSelectionStateChanged(selected);
    }
  }

  /**
   * 触发交互
   */
  triggerInteraction(interactionType: InteractionType, data?: any): void {
    if (!this.isInteractive) {
      return;
    }

    this.lastInteractionTime = Date.now();
    this.interactionCount++;
    
    this.onInteraction(interactionType, data);
  }

  /**
   * 播放动画
   */
  playAnimation(animationName: string, loop: boolean = false): boolean {
    if (!this.renderOptions.enableAnimations) {
      return false;
    }

    const animation = this.animations.get(animationName);
    if (!animation) {
      console.warn(`动画不存在: ${animationName}`);
      return false;
    }

    this.currentAnimation = animationName;
    
    // 这里应该实际播放动画
    console.log(`播放NFT动画: ${animationName}, 循环: ${loop}`);
    
    return true;
  }

  /**
   * 停止动画
   */
  stopAnimation(): void {
    if (this.currentAnimation) {
      console.log(`停止NFT动画: ${this.currentAnimation}`);
      this.currentAnimation = null;
    }
  }

  /**
   * 获取渲染选项
   */
  getRenderOptions(): NFTRenderOptions {
    return { ...this.renderOptions };
  }

  /**
   * 更新渲染选项
   */
  updateRenderOptions(options: Partial<NFTRenderOptions>): void {
    this.renderOptions = { ...this.renderOptions, ...options };
    
    // 如果NFT已显示，需要重新渲染
    if (this.isDisplayed) {
      this.requestRerender();
    }
  }

  /**
   * 获取显示配置
   */
  getDisplayConfig(): NFTDisplayConfig {
    return { ...this.displayConfig };
  }

  /**
   * 更新显示配置
   */
  updateDisplayConfig(config: Partial<NFTDisplayConfig>): void {
    this.displayConfig = { ...this.displayConfig, ...config };
  }

  /**
   * 获取边界框
   */
  getBoundingBox(): any {
    return this.boundingBox;
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats(): {
    loadTime: number;
    renderTime: number;
    interactionCount: number;
    isDisplayed: boolean;
    memoryUsage?: number;
  } {
    return {
      loadTime: this.renderTime,
      renderTime: this.renderTime,
      interactionCount: this.interactionCount,
      isDisplayed: this.isDisplayed,
      // memoryUsage 需要从渲染器获取
    };
  }

  /**
   * 检查是否支持交互
   */
  private hasInteractionCapabilities(): boolean {
    const metadata = this.nftToken.metadata;
    return metadata.dl_engine_data?.interaction_metadata?.interaction_types?.length > 0;
  }

  /**
   * 更新动画
   */
  private updateAnimations(): void {
    if (!this.renderOptions.enableAnimations || !this.currentAnimation) {
      return;
    }

    // 这里应该更新当前播放的动画
    // 具体实现取决于渲染引擎
  }

  /**
   * 更新交互状态
   */
  private updateInteractionState(): void {
    // 检查交互超时
    const now = Date.now();
    if (this.lastInteractionTime > 0 && now - this.lastInteractionTime > 5000) {
      // 5秒无交互，重置某些状态
      this.isHovered = false;
    }
  }

  /**
   * 更新渲染对象
   */
  private updateRenderObject(): void {
    if (!this.renderObject) {
      return;
    }

    // 根据状态更新渲染对象的视觉效果
    if (this.isSelected) {
      // 添加选中效果
    }
    
    if (this.isHovered) {
      // 添加悬停效果
    }
  }

  /**
   * 更新边界框
   */
  private updateBoundingBox(): void {
    if (this.renderObject) {
      // 计算渲染对象的边界框
      // 具体实现取决于渲染引擎
      this.boundingBox = {
        min: { x: -1, y: -1, z: -1 },
        max: { x: 1, y: 1, z: 1 }
      };
    }
  }

  /**
   * 悬停状态改变回调
   */
  private onHoverStateChanged(hovered: boolean): void {
    console.log(`NFT ${this.nftToken.tokenId} 悬停状态: ${hovered}`);
    
    // 触发悬停事件
    if (hovered) {
      this.triggerInteraction(InteractionType.HOVER);
    }
  }

  /**
   * 选中状态改变回调
   */
  private onSelectionStateChanged(selected: boolean): void {
    console.log(`NFT ${this.nftToken.tokenId} 选中状态: ${selected}`);
    
    // 触发点击事件
    if (selected) {
      this.triggerInteraction(InteractionType.CLICK);
    }
  }

  /**
   * 交互回调
   */
  private onInteraction(interactionType: InteractionType, data?: any): void {
    console.log(`NFT ${this.nftToken.tokenId} 交互: ${interactionType}`, data);
    
    // 根据交互类型执行相应操作
    switch (interactionType) {
      case InteractionType.CLICK:
        // 处理点击交互
        break;
      case InteractionType.HOVER:
        // 处理悬停交互
        break;
      case InteractionType.DRAG:
        // 处理拖拽交互
        break;
      // ... 其他交互类型
    }
  }

  /**
   * 请求重新渲染
   */
  private requestRerender(): void {
    // 通知渲染系统需要重新渲染此NFT
    console.log(`请求重新渲染NFT: ${this.nftToken.tokenId}`);
  }

  /**
   * 序列化组件数据
   */
  serialize(): any {
    return {
      type: this.type,
      nftToken: this.nftToken,
      renderOptions: this.renderOptions,
      displayConfig: this.displayConfig,
      isLoaded: this.isLoaded,
      isDisplayed: this.isDisplayed,
      isInteractive: this.isInteractive,
      interactionCount: this.interactionCount
    };
  }

  /**
   * 反序列化组件数据
   */
  deserialize(data: any): void {
    this.nftToken = data.nftToken;
    this.renderOptions = data.renderOptions || this.renderOptions;
    this.displayConfig = data.displayConfig || this.displayConfig;
    this.isLoaded = data.isLoaded || false;
    this.isDisplayed = data.isDisplayed || false;
    this.isInteractive = data.isInteractive || false;
    this.interactionCount = data.interactionCount || 0;
  }

  /**
   * 组件销毁
   */
  destroy(): void {
    // 清理渲染对象
    this.renderObject = null;
    this.boundingBox = null;
    
    // 清理动画
    this.animations.clear();
    this.currentAnimation = null;
    
    // 重置状态
    this.isLoaded = false;
    this.isDisplayed = false;
    this.isInteractive = false;
    
    console.log(`NFT组件已销毁: ${this.nftToken.tokenId}`);
  }
}

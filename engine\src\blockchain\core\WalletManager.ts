/**
 * 钱包管理器 - 处理各种Web3钱包的连接和交互
 */

import { EventEmitter } from '../../utils/EventEmitter';
import { WalletAdapter } from '../wallet/WalletAdapter';
import { MetaMaskAdapter } from '../wallet/MetaMaskAdapter';
import { WalletConnectAdapter } from '../wallet/WalletConnectAdapter';
import { CoinbaseWalletAdapter } from '../wallet/CoinbaseWalletAdapter';
import { 
  WalletType, 
  WalletStatus, 
  BlockchainNetwork, 
  Transaction, 
  BlockchainResult,
  BlockchainEventType
} from '../types/BlockchainTypes';

export class WalletManager extends EventEmitter {
  private adapters: Map<WalletType, WalletAdapter> = new Map();
  private currentAdapter: WalletAdapter | null = null;
  private status: WalletStatus;
  private blockchainManager: any; // 避免循环引用

  constructor(blockchainManager: any) {
    super();
    this.blockchainManager = blockchainManager;
    
    this.status = {
      isConnected: false,
      address: null,
      balance: '0',
      chainId: null,
      walletType: null
    };
  }

  /**
   * 初始化钱包管理器
   */
  async initialize(): Promise<void> {
    try {
      console.log('初始化钱包管理器...');
      
      // 初始化各种钱包适配器
      this.adapters.set(WalletType.METAMASK, new MetaMaskAdapter());
      this.adapters.set(WalletType.WALLET_CONNECT, new WalletConnectAdapter());
      this.adapters.set(WalletType.COINBASE_WALLET, new CoinbaseWalletAdapter());
      
      // 检查是否有已连接的钱包
      await this.checkExistingConnection();
      
      console.log('钱包管理器初始化完成');
    } catch (error) {
      console.error('钱包管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 连接钱包
   */
  async connect(walletType?: string): Promise<BlockchainResult<string>> {
    try {
      let adapter: WalletAdapter | undefined;
      
      if (walletType) {
        adapter = this.adapters.get(walletType as WalletType);
        if (!adapter) {
          throw new Error(`不支持的钱包类型: ${walletType}`);
        }
      } else {
        // 自动检测可用的钱包
        adapter = await this.detectAvailableWallet();
        if (!adapter) {
          throw new Error('未检测到可用的钱包');
        }
      }

      // 检查钱包是否可用
      const isAvailable = await adapter.isAvailable();
      if (!isAvailable) {
        throw new Error('钱包不可用，请确保已安装并启用');
      }

      // 连接钱包
      const address = await adapter.connect();
      
      // 获取网络信息
      const chainId = await adapter.getChainId();
      const balance = await adapter.getBalance();
      
      // 更新状态
      this.currentAdapter = adapter;
      this.status = {
        isConnected: true,
        address,
        balance,
        chainId,
        walletType: adapter.getType()
      };

      // 设置事件监听
      this.setupAdapterListeners(adapter);

      console.log(`钱包连接成功: ${address}`);
      
      return {
        success: true,
        data: address
      };
      
    } catch (error) {
      console.error('连接钱包失败:', error);
      return {
        success: false,
        error: {
          code: -1,
          message: error instanceof Error ? error.message : '连接钱包失败'
        }
      };
    }
  }

  /**
   * 断开钱包连接
   */
  async disconnect(): Promise<void> {
    try {
      if (this.currentAdapter) {
        await this.currentAdapter.disconnect();
        this.removeAdapterListeners(this.currentAdapter);
        this.currentAdapter = null;
      }

      this.status = {
        isConnected: false,
        address: null,
        balance: '0',
        chainId: null,
        walletType: null
      };

      this.emit(BlockchainEventType.WALLET_DISCONNECTED);
      console.log('钱包已断开连接');
      
    } catch (error) {
      console.error('断开钱包连接失败:', error);
    }
  }

  /**
   * 切换网络
   */
  async switchNetwork(network: BlockchainNetwork): Promise<BlockchainResult<void>> {
    try {
      if (!this.currentAdapter) {
        throw new Error('钱包未连接');
      }

      await this.currentAdapter.switchNetwork(network);
      
      // 更新链ID
      this.status.chainId = network.chainId;
      
      return { success: true };
      
    } catch (error) {
      console.error('切换网络失败:', error);
      return {
        success: false,
        error: {
          code: -1,
          message: error instanceof Error ? error.message : '切换网络失败'
        }
      };
    }
  }

  /**
   * 发送交易
   */
  async sendTransaction(transaction: Partial<Transaction>): Promise<BlockchainResult<string>> {
    try {
      if (!this.currentAdapter) {
        throw new Error('钱包未连接');
      }

      const txHash = await this.currentAdapter.sendTransaction(transaction);
      
      return {
        success: true,
        data: txHash,
        transactionHash: txHash
      };
      
    } catch (error) {
      console.error('发送交易失败:', error);
      return {
        success: false,
        error: {
          code: -1,
          message: error instanceof Error ? error.message : '发送交易失败'
        }
      };
    }
  }

  /**
   * 签名消息
   */
  async signMessage(message: string): Promise<BlockchainResult<string>> {
    try {
      if (!this.currentAdapter) {
        throw new Error('钱包未连接');
      }

      const signature = await this.currentAdapter.signMessage(message);
      
      return {
        success: true,
        data: signature
      };
      
    } catch (error) {
      console.error('签名消息失败:', error);
      return {
        success: false,
        error: {
          code: -1,
          message: error instanceof Error ? error.message : '签名消息失败'
        }
      };
    }
  }

  /**
   * 获取余额
   */
  async getBalance(address?: string): Promise<string> {
    try {
      if (!this.currentAdapter) {
        throw new Error('钱包未连接');
      }

      const balance = await this.currentAdapter.getBalance(address);
      
      if (!address || address === this.status.address) {
        this.status.balance = balance;
      }
      
      return balance;
      
    } catch (error) {
      console.error('获取余额失败:', error);
      return '0';
    }
  }

  /**
   * 获取当前钱包状态
   */
  getStatus(): WalletStatus {
    return { ...this.status };
  }

  /**
   * 获取当前钱包类型
   */
  getCurrentWalletType(): string | null {
    return this.status.walletType;
  }

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.status.isConnected;
  }

  /**
   * 获取当前地址
   */
  getCurrentAddress(): string | null {
    return this.status.address;
  }

  /**
   * 检查现有连接
   */
  private async checkExistingConnection(): Promise<void> {
    try {
      // 检查各个钱包是否已经连接
      for (const [type, adapter] of this.adapters) {
        const isAvailable = await adapter.isAvailable();
        if (isAvailable) {
          const isConnected = await adapter.isConnected();
          if (isConnected) {
            console.log(`检测到已连接的钱包: ${type}`);
            await this.connect(type);
            break;
          }
        }
      }
    } catch (error) {
      console.error('检查现有连接失败:', error);
    }
  }

  /**
   * 自动检测可用的钱包
   */
  private async detectAvailableWallet(): Promise<WalletAdapter | null> {
    // 按优先级检查钱包
    const priorityOrder = [
      WalletType.METAMASK,
      WalletType.COINBASE_WALLET,
      WalletType.WALLET_CONNECT
    ];

    for (const walletType of priorityOrder) {
      const adapter = this.adapters.get(walletType);
      if (adapter) {
        const isAvailable = await adapter.isAvailable();
        if (isAvailable) {
          return adapter;
        }
      }
    }

    return null;
  }

  /**
   * 设置适配器事件监听
   */
  private setupAdapterListeners(adapter: WalletAdapter): void {
    adapter.on('accountsChanged', (accounts: string[]) => {
      if (accounts.length === 0) {
        this.disconnect();
      } else if (accounts[0] !== this.status.address) {
        this.status.address = accounts[0];
        this.emit(BlockchainEventType.ACCOUNT_CHANGED, accounts[0]);
      }
    });

    adapter.on('chainChanged', (chainId: number) => {
      this.status.chainId = chainId;
      this.emit(BlockchainEventType.NETWORK_CHANGED, chainId);
    });

    adapter.on('disconnect', () => {
      this.disconnect();
    });
  }

  /**
   * 移除适配器事件监听
   */
  private removeAdapterListeners(adapter: WalletAdapter): void {
    adapter.removeAllListeners();
  }
}

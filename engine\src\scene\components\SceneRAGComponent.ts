/**
 * 场景RAG应用组件
 * 管理场景中的RAG应用配置和运行状态
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { EventEmitter } from '../../utils/EventEmitter';
import type { SceneAvatarConfig, AvatarInstance } from '../SceneAvatarManager';

/**
 * RAG应用配置
 */
export interface RAGApplicationConfig {
  /** 应用ID */
  id: string;
  /** 应用名称 */
  name: string;
  /** 应用描述 */
  description: string;
  /** 知识库ID */
  knowledgeBaseId: string;
  /** 数字人配置列表 */
  avatars: SceneAvatarConfig[];
  /** 是否自动启动 */
  autoStart: boolean;
  /** 最大并发会话数 */
  maxConcurrentSessions: number;
  /** 会话超时时间（秒） */
  sessionTimeout: number;
  /** 欢迎消息 */
  welcomeMessage?: string;
  /** 告别消息 */
  farewellMessage?: string;
}

/**
 * RAG应用状态
 */
export interface RAGApplicationState {
  /** 应用状态 */
  status: 'stopped' | 'starting' | 'running' | 'stopping' | 'error';
  /** 活跃会话数 */
  activeSessions: number;
  /** 总消息数 */
  totalMessages: number;
  /** 启动时间 */
  startTime?: number;
  /** 错误信息 */
  error?: string;
}

/**
 * 会话信息
 */
export interface SessionInfo {
  /** 会话ID */
  id: string;
  /** 用户ID */
  userId?: string;
  /** 关联的数字人ID */
  avatarId: string;
  /** 开始时间 */
  startTime: number;
  /** 最后活动时间 */
  lastActivity: number;
  /** 消息数量 */
  messageCount: number;
  /** 会话状态 */
  status: 'active' | 'idle' | 'ended';
}

/**
 * 场景RAG应用组件
 */
export class SceneRAGComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE = 'SceneRAGComponent';

  /** RAG应用配置 */
  private config: RAGApplicationConfig;
  
  /** 应用状态 */
  private state: RAGApplicationState;
  
  /** 活跃会话映射 */
  private sessions: Map<string, SessionInfo> = new Map();
  
  /** 数字人实例映射 */
  private avatarInstances: Map<string, AvatarInstance> = new Map();
  
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  
  /** 会话超时检查间隔 */
  private sessionCheckInterval: number = 30000; // 30秒
  
  /** 上次会话检查时间 */
  private lastSessionCheck: number = 0;

  /**
   * 构造函数
   */
  constructor(entity: Entity, config: RAGApplicationConfig) {
    super(entity);
    
    this.config = config;
    this.state = {
      status: 'stopped',
      activeSessions: 0,
      totalMessages: 0,
    };
  }

  /**
   * 初始化组件
   */
  public async initialize(): Promise<void> {
    try {
      // 获取场景和数字人管理器
      const scene = this.entity.getScene();
      if (!scene) {
        throw new Error('实体未关联到场景');
      }

      const avatarManager = scene.getAvatarManager();
      
      // 添加数字人到场景
      for (const avatarConfig of this.config.avatars) {
        const instance = await avatarManager.addAvatar(avatarConfig);
        this.avatarInstances.set(avatarConfig.avatarId, instance);
        
        // 监听数字人事件
        avatarManager.on('conversationStarted', this.onConversationStarted.bind(this));
        avatarManager.on('conversationEnded', this.onConversationEnded.bind(this));
        avatarManager.on('messageProcessed', this.onMessageProcessed.bind(this));
      }

      // 如果配置了自动启动，则启动应用
      if (this.config.autoStart) {
        await this.start();
      }

      console.log(`RAG应用 ${this.config.name} 初始化完成`);
    } catch (error) {
      console.error('RAG应用初始化失败:', error);
      this.state.status = 'error';
      this.state.error = error.message;
      throw error;
    }
  }

  /**
   * 启动RAG应用
   */
  public async start(): Promise<void> {
    if (this.state.status === 'running') {
      return;
    }

    try {
      this.state.status = 'starting';
      this.eventEmitter.emit('statusChanged', this.state);

      // 激活所有数字人
      const scene = this.entity.getScene();
      const avatarManager = scene?.getAvatarManager();
      
      if (avatarManager) {
        for (const avatarConfig of this.config.avatars) {
          await avatarManager.activateAvatar(avatarConfig.avatarId);
        }
      }

      this.state.status = 'running';
      this.state.startTime = Date.now();
      this.eventEmitter.emit('statusChanged', this.state);
      this.eventEmitter.emit('started');

      console.log(`RAG应用 ${this.config.name} 已启动`);
    } catch (error) {
      this.state.status = 'error';
      this.state.error = error.message;
      this.eventEmitter.emit('statusChanged', this.state);
      throw error;
    }
  }

  /**
   * 停止RAG应用
   */
  public async stop(): Promise<void> {
    if (this.state.status === 'stopped') {
      return;
    }

    try {
      this.state.status = 'stopping';
      this.eventEmitter.emit('statusChanged', this.state);

      // 结束所有活跃会话
      for (const session of this.sessions.values()) {
        if (session.status === 'active') {
          await this.endSession(session.id);
        }
      }

      // 停用所有数字人
      const scene = this.entity.getScene();
      const avatarManager = scene?.getAvatarManager();
      
      if (avatarManager) {
        for (const avatarConfig of this.config.avatars) {
          await avatarManager.deactivateAvatar(avatarConfig.avatarId);
        }
      }

      this.state.status = 'stopped';
      this.state.startTime = undefined;
      this.eventEmitter.emit('statusChanged', this.state);
      this.eventEmitter.emit('stopped');

      console.log(`RAG应用 ${this.config.name} 已停止`);
    } catch (error) {
      this.state.status = 'error';
      this.state.error = error.message;
      this.eventEmitter.emit('statusChanged', this.state);
      throw error;
    }
  }

  /**
   * 开始会话
   */
  public async startSession(avatarId: string, userId?: string): Promise<string> {
    if (this.state.status !== 'running') {
      throw new Error('RAG应用未运行');
    }

    if (this.state.activeSessions >= this.config.maxConcurrentSessions) {
      throw new Error('已达到最大并发会话数');
    }

    const scene = this.entity.getScene();
    const avatarManager = scene?.getAvatarManager();
    
    if (!avatarManager) {
      throw new Error('数字人管理器不可用');
    }

    // 开始数字人对话
    const sessionId = await avatarManager.startConversation(avatarId, userId);

    // 创建会话信息
    const session: SessionInfo = {
      id: sessionId,
      userId,
      avatarId,
      startTime: Date.now(),
      lastActivity: Date.now(),
      messageCount: 0,
      status: 'active',
    };

    this.sessions.set(sessionId, session);
    this.state.activeSessions++;
    
    this.eventEmitter.emit('sessionStarted', session);
    return sessionId;
  }

  /**
   * 结束会话
   */
  public async endSession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`会话 ${sessionId} 不存在`);
    }

    const scene = this.entity.getScene();
    const avatarManager = scene?.getAvatarManager();
    
    if (avatarManager) {
      await avatarManager.endConversation(session.avatarId);
    }

    session.status = 'ended';
    this.sessions.delete(sessionId);
    this.state.activeSessions--;
    
    this.eventEmitter.emit('sessionEnded', session);
  }

  /**
   * 发送消息
   */
  public async sendMessage(sessionId: string, message: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`会话 ${sessionId} 不存在`);
    }

    if (session.status !== 'active') {
      throw new Error(`会话 ${sessionId} 不活跃`);
    }

    const scene = this.entity.getScene();
    const avatarManager = scene?.getAvatarManager();
    
    if (avatarManager) {
      await avatarManager.sendMessage(session.avatarId, message, session.userId);
    }

    // 更新会话信息
    session.lastActivity = Date.now();
    session.messageCount++;
    this.state.totalMessages++;
  }

  /**
   * 更新组件
   */
  public update(deltaTime: number): void {
    if (this.state.status !== 'running') {
      return;
    }

    // 检查会话超时
    const now = Date.now();
    if (now - this.lastSessionCheck > this.sessionCheckInterval) {
      this.checkSessionTimeouts();
      this.lastSessionCheck = now;
    }
  }

  /**
   * 检查会话超时
   */
  private checkSessionTimeouts(): void {
    const now = Date.now();
    const timeoutMs = this.config.sessionTimeout * 1000;

    for (const [sessionId, session] of this.sessions.entries()) {
      if (session.status === 'active' && now - session.lastActivity > timeoutMs) {
        session.status = 'idle';
        this.eventEmitter.emit('sessionTimeout', session);
        
        // 自动结束超时会话
        this.endSession(sessionId).catch(console.error);
      }
    }
  }

  /**
   * 对话开始事件处理
   */
  private onConversationStarted(event: any): void {
    // 更新会话状态
    const session = this.sessions.get(event.sessionId);
    if (session) {
      session.status = 'active';
      session.lastActivity = Date.now();
    }
  }

  /**
   * 对话结束事件处理
   */
  private onConversationEnded(event: any): void {
    // 清理会话
    const session = this.sessions.get(event.sessionId);
    if (session) {
      this.sessions.delete(event.sessionId);
      this.state.activeSessions--;
    }
  }

  /**
   * 消息处理事件处理
   */
  private onMessageProcessed(event: any): void {
    // 更新统计信息
    this.state.totalMessages++;
    
    // 更新会话活动时间
    const session = this.sessions.get(event.sessionId);
    if (session) {
      session.lastActivity = Date.now();
      session.messageCount++;
    }
  }

  /**
   * 获取配置
   */
  public getConfig(): RAGApplicationConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  public updateConfig(config: Partial<RAGApplicationConfig>): void {
    this.config = { ...this.config, ...config };
    this.eventEmitter.emit('configUpdated', this.config);
  }

  /**
   * 获取状态
   */
  public getState(): RAGApplicationState {
    return { ...this.state };
  }

  /**
   * 获取会话列表
   */
  public getSessions(): SessionInfo[] {
    return Array.from(this.sessions.values());
  }

  /**
   * 获取活跃会话
   */
  public getActiveSessions(): SessionInfo[] {
    return Array.from(this.sessions.values()).filter(s => s.status === 'active');
  }

  /**
   * 监听事件
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听
   */
  public off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 销毁组件
   */
  public dispose(): void {
    // 停止应用
    this.stop().catch(console.error);

    // 清理资源
    this.sessions.clear();
    this.avatarInstances.clear();
    this.eventEmitter.removeAllListeners();

    super.dispose();
  }
}
